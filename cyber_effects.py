"""
تأثيرات سايبر إضافية للواجهة
يمكن استخدامها لإضافة المزيد من التأثيرات البصرية
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox
import time
import threading

class CyberEffects:
    def __init__(self):
        self.cyber_colors = {
            'neon_blue': '#00FFFF',
            'neon_green': '#00FF41',
            'neon_purple': '#BF00FF',
            'dark_bg': '#0A0A0A',
            'cyber_gray': '#1A1A1A',
            'matrix_green': '#00FF00',
            'electric_blue': '#0080FF',
            'cyber_red': '#FF0040'
        }
    
    def create_glitch_text(self, parent, text, font_size=16):
        """إنشاء نص بتأثير الخلل (Glitch)"""
        glitch_frame = ctk.CTkFrame(parent, fg_color="transparent")
        
        # النص الأساسي
        main_text = ctk.CTkLabel(
            glitch_frame,
            text=text,
            font=ctk.CTkFont(size=font_size, family="Consolas", weight="bold"),
            text_color=self.cyber_colors['neon_blue']
        )
        main_text.pack()
        
        return glitch_frame
    
    def create_matrix_rain_effect(self, parent, width=50, height=20):
        """إنشاء تأثير مطر الماتريكس"""
        matrix_frame = ctk.CTkFrame(
            parent, 
            width=width, 
            height=height,
            fg_color=self.cyber_colors['dark_bg']
        )
        
        # إنشاء نص الماتريكس
        matrix_chars = "01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン"
        
        matrix_text = ctk.CTkLabel(
            matrix_frame,
            text="".join([matrix_chars[i % len(matrix_chars)] for i in range(20)]),
            font=ctk.CTkFont(size=8, family="Consolas"),
            text_color=self.cyber_colors['matrix_green']
        )
        matrix_text.pack(fill="both", expand=True)
        
        return matrix_frame
    
    def create_cyber_button(self, parent, text, command=None, width=200, height=40):
        """إنشاء زر بتصميم سايبر متقدم"""
        button = ctk.CTkButton(
            parent,
            text=f"◢◤ {text} ◥◣",
            width=width,
            height=height,
            font=ctk.CTkFont(size=14, family="Consolas", weight="bold"),
            fg_color=self.cyber_colors['neon_blue'],
            hover_color=self.cyber_colors['electric_blue'],
            text_color="black",
            border_width=2,
            border_color=self.cyber_colors['neon_green'],
            command=command
        )
        return button
    
    def create_cyber_entry(self, parent, placeholder="", width=300, height=40):
        """إنشاء حقل إدخال بتصميم سايبر"""
        entry = ctk.CTkEntry(
            parent,
            placeholder_text=f">>> {placeholder}...",
            width=width,
            height=height,
            font=ctk.CTkFont(size=12, family="Consolas"),
            fg_color=("#2b2b2b", "#1a1a1a"),
            border_color=self.cyber_colors['neon_blue'],
            border_width=2,
            text_color=self.cyber_colors['neon_green']
        )
        return entry
    
    def create_cyber_frame(self, parent, border_color='neon_blue'):
        """إنشاء إطار بتصميم سايبر"""
        frame = ctk.CTkFrame(
            parent,
            corner_radius=15,
            fg_color=("#1a1a1a", "#0d1117"),
            border_width=2,
            border_color=self.cyber_colors[border_color]
        )
        return frame
    
    def show_cyber_message(self, title, message, msg_type="info"):
        """عرض رسالة بتصميم سايبر"""
        if msg_type == "error":
            icon = "⚠"
            title = f"{icon} CYBER ERROR"
        elif msg_type == "success":
            icon = "✓"
            title = f"{icon} OPERATION SUCCESS"
        elif msg_type == "warning":
            icon = "⚠"
            title = f"{icon} SYSTEM WARNING"
        else:
            icon = "ℹ"
            title = f"{icon} SYSTEM INFO"
        
        formatted_message = f"[SYSTEM] {message}"
        
        if msg_type == "error":
            messagebox.showerror(title, formatted_message)
        elif msg_type == "success":
            messagebox.showinfo(title, formatted_message)
        elif msg_type == "warning":
            messagebox.showwarning(title, formatted_message)
        else:
            messagebox.showinfo(title, formatted_message)
    
    def create_loading_animation(self, parent, text="Loading"):
        """إنشاء رسوم متحركة للتحميل"""
        loading_frame = ctk.CTkFrame(parent, fg_color="transparent")
        
        loading_text = ctk.CTkLabel(
            loading_frame,
            text=f"[SYSTEM] {text}...",
            font=ctk.CTkFont(size=12, family="Consolas"),
            text_color=self.cyber_colors['neon_green']
        )
        loading_text.pack()
        
        # شريط التقدم
        progress_bar = ctk.CTkProgressBar(
            loading_frame,
            width=200,
            height=10,
            progress_color=self.cyber_colors['neon_blue']
        )
        progress_bar.pack(pady=10)
        progress_bar.set(0.5)  # 50% كمثال
        
        return loading_frame, progress_bar
    
    def create_cyber_separator(self, parent, color='neon_blue'):
        """إنشاء خط فاصل سايبر"""
        separator = ctk.CTkLabel(
            parent,
            text="═══════════════════════════════════",
            font=ctk.CTkFont(size=12, family="Consolas"),
            text_color=self.cyber_colors[color]
        )
        return separator
    
    def create_status_indicator(self, parent, status="ONLINE"):
        """إنشاء مؤشر حالة النظام"""
        status_frame = ctk.CTkFrame(parent, fg_color="transparent")
        
        if status == "ONLINE":
            color = self.cyber_colors['neon_green']
            symbol = "●"
        elif status == "OFFLINE":
            color = self.cyber_colors['cyber_red']
            symbol = "●"
        else:
            color = self.cyber_colors['neon_blue']
            symbol = "◐"
        
        status_label = ctk.CTkLabel(
            status_frame,
            text=f"{symbol} [STATUS] {status}",
            font=ctk.CTkFont(size=10, family="Consolas"),
            text_color=color
        )
        status_label.pack()
        
        return status_frame
    
    def create_cyber_title(self, parent, title, size=24, color='neon_blue'):
        """إنشاء عنوان بتصميم سايبر"""
        title_frame = ctk.CTkFrame(parent, fg_color="transparent")
        
        # العنوان الرئيسي
        main_title = ctk.CTkLabel(
            title_frame,
            text=f"◢◤ {title} ◥◣",
            font=ctk.CTkFont(size=size, weight="bold", family="Consolas"),
            text_color=self.cyber_colors[color]
        )
        main_title.pack()
        
        # خط تحت العنوان
        line = ctk.CTkFrame(title_frame, height=2, fg_color=self.cyber_colors[color])
        line.pack(fill="x", padx=50, pady=(5, 0))
        
        return title_frame

# مثال على الاستخدام
if __name__ == "__main__":
    # إنشاء نافذة تجريبية
    root = ctk.CTk()
    root.title("Cyber Effects Demo")
    root.geometry("500x600")
    ctk.set_appearance_mode("dark")
    
    effects = CyberEffects()
    
    # إنشاء إطار رئيسي
    main_frame = effects.create_cyber_frame(root)
    main_frame.pack(fill="both", expand=True, padx=20, pady=20)
    
    # عنوان
    title = effects.create_cyber_title(main_frame, "CYBER DEMO")
    title.pack(pady=20)
    
    # زر
    button = effects.create_cyber_button(main_frame, "TEST BUTTON")
    button.pack(pady=10)
    
    # حقل إدخال
    entry = effects.create_cyber_entry(main_frame, "Enter data")
    entry.pack(pady=10)
    
    # مؤشر الحالة
    status = effects.create_status_indicator(main_frame, "ONLINE")
    status.pack(pady=10)
    
    root.mainloop()
