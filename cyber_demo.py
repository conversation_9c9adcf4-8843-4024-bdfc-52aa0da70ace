"""
عرض تجريبي للتأثيرات السايبرية
يعرض جميع العناصر المستخدمة في التطبيق
"""

import customtkinter as ctk
from cyber_effects import CyberEffects
import time
import threading

class CyberDemo:
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("◢◤ CYBER EFFECTS DEMO ◥◣")
        self.root.geometry("600x700")
        self.root.resizable(False, False)
        
        # إعداد المظهر السايبر
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")
        
        # إنشاء كائن التأثيرات
        self.effects = CyberEffects()
        
        # إنشاء الواجهة
        self.create_demo_interface()
    
    def create_demo_interface(self):
        """إنشاء واجهة العرض التجريبي"""
        # الإطار الرئيسي
        main_frame = self.effects.create_cyber_frame(self.root, 'neon_blue')
        main_frame.pack(fill="both", expand=True, padx=15, pady=15)
        
        # العنوان الرئيسي
        title = self.effects.create_cyber_title(main_frame, "CYBER EFFECTS SHOWCASE", 28, 'neon_blue')
        title.pack(pady=(20, 10))
        
        # مؤشر الحالة
        status = self.effects.create_status_indicator(main_frame, "ONLINE")
        status.pack(pady=5)
        
        # رسالة النظام
        system_msg = ctk.CTkLabel(
            main_frame,
            text="[SYSTEM] Demonstrating cyber interface elements...",
            font=ctk.CTkFont(size=11, family="Consolas"),
            text_color=self.effects.cyber_colors['matrix_green']
        )
        system_msg.pack(pady=10)
        
        # خط فاصل
        separator = self.effects.create_cyber_separator(main_frame, 'neon_blue')
        separator.pack(pady=15)
        
        # قسم حقول الإدخال
        input_section = ctk.CTkLabel(
            main_frame,
            text="┌─ INPUT FIELDS DEMO ─┐",
            font=ctk.CTkFont(size=14, family="Consolas", weight="bold"),
            text_color=self.effects.cyber_colors['neon_green']
        )
        input_section.pack(pady=(10, 5))
        
        # حقل إدخال تجريبي
        demo_entry1 = self.effects.create_cyber_entry(main_frame, "Username/Email", 350, 45)
        demo_entry1.pack(pady=5)
        
        demo_entry2 = self.effects.create_cyber_entry(main_frame, "Password", 350, 45)
        demo_entry2.pack(pady=5)
        
        # خط فاصل
        separator2 = self.effects.create_cyber_separator(main_frame, 'neon_green')
        separator2.pack(pady=15)
        
        # قسم الأزرار
        button_section = ctk.CTkLabel(
            main_frame,
            text="┌─ CYBER BUTTONS DEMO ─┐",
            font=ctk.CTkFont(size=14, family="Consolas", weight="bold"),
            text_color=self.effects.cyber_colors['neon_purple']
        )
        button_section.pack(pady=(10, 5))
        
        # أزرار تجريبية
        btn1 = self.effects.create_cyber_button(
            main_frame, 
            "LOGIN SYSTEM", 
            command=self.demo_login,
            width=300, 
            height=45
        )
        btn1.pack(pady=5)
        
        btn2 = ctk.CTkButton(
            main_frame,
            text="⚡ CREATE ACCOUNT ⚡",
            width=300,
            height=40,
            font=ctk.CTkFont(size=14, family="Consolas"),
            fg_color="transparent",
            border_width=2,
            border_color=self.effects.cyber_colors['neon_green'],
            text_color=self.effects.cyber_colors['neon_green'],
            hover_color=("#2b2b2b", "#1a1a1a"),
            command=self.demo_register
        )
        btn2.pack(pady=5)
        
        btn3 = ctk.CTkButton(
            main_frame,
            text="⚠ SYSTEM ALERT",
            width=200,
            height=35,
            font=ctk.CTkFont(size=12, family="Consolas"),
            fg_color=self.effects.cyber_colors['neon_purple'],
            hover_color="#8B008B",
            text_color="white",
            command=self.demo_alert
        )
        btn3.pack(pady=5)
        
        # خط فاصل
        separator3 = self.effects.create_cyber_separator(main_frame, 'neon_purple')
        separator3.pack(pady=15)
        
        # قسم الرسائل
        message_section = ctk.CTkLabel(
            main_frame,
            text="┌─ MESSAGE SYSTEM DEMO ─┐",
            font=ctk.CTkFont(size=14, family="Consolas", weight="bold"),
            text_color=self.effects.cyber_colors['electric_blue']
        )
        message_section.pack(pady=(10, 5))
        
        # أزرار الرسائل
        msg_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        msg_frame.pack(pady=10)
        
        success_btn = ctk.CTkButton(
            msg_frame,
            text="✓ SUCCESS",
            width=100,
            height=30,
            font=ctk.CTkFont(size=10, family="Consolas"),
            fg_color=self.effects.cyber_colors['neon_green'],
            text_color="black",
            command=lambda: self.effects.show_cyber_message("Success", "Operation completed successfully!", "success")
        )
        success_btn.pack(side="left", padx=5)
        
        error_btn = ctk.CTkButton(
            msg_frame,
            text="⚠ ERROR",
            width=100,
            height=30,
            font=ctk.CTkFont(size=10, family="Consolas"),
            fg_color=self.effects.cyber_colors['cyber_red'],
            text_color="white",
            command=lambda: self.effects.show_cyber_message("Error", "System error detected!", "error")
        )
        error_btn.pack(side="left", padx=5)
        
        info_btn = ctk.CTkButton(
            msg_frame,
            text="ℹ INFO",
            width=100,
            height=30,
            font=ctk.CTkFont(size=10, family="Consolas"),
            fg_color=self.effects.cyber_colors['neon_blue'],
            text_color="black",
            command=lambda: self.effects.show_cyber_message("Info", "System information message.", "info")
        )
        info_btn.pack(side="left", padx=5)
        
        # رسالة الحالة النهائية
        final_status = ctk.CTkLabel(
            main_frame,
            text="[STATUS] Demo interface loaded successfully. All systems operational.",
            font=ctk.CTkFont(size=9, family="Consolas"),
            text_color=self.effects.cyber_colors['matrix_green']
        )
        final_status.pack(pady=(15, 10))
    
    def demo_login(self):
        """عرض تجريبي لتسجيل الدخول"""
        self.effects.show_cyber_message("Login Demo", "This would initiate the login process!", "info")
    
    def demo_register(self):
        """عرض تجريبي للتسجيل"""
        self.effects.show_cyber_message("Register Demo", "This would open the registration form!", "info")
    
    def demo_alert(self):
        """عرض تجريبي للتنبيه"""
        self.effects.show_cyber_message("System Alert", "This is a system alert demonstration!", "warning")
    
    def run(self):
        """تشغيل العرض التجريبي"""
        self.root.mainloop()

if __name__ == "__main__":
    demo = CyberDemo()
    demo.run()
