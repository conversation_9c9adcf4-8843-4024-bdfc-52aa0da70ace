"""
ملف اختبار للتأكد من عمل جميع وظائف البرنامج
"""

import sqlite3
import bcrypt
import os
import json
from datetime import datetime, timedelta

def test_database():
    """اختبار قاعدة البيانات"""
    print("🔍 اختبار قاعدة البيانات...")
    
    # الاتصال بقاعدة البيانات
    conn = sqlite3.connect('users.db')
    cursor = conn.cursor()
    
    # التحقق من وجود الجدول
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
    table_exists = cursor.fetchone()
    
    if table_exists:
        print("✅ جدول المستخدمين موجود")
        
        # عرض عدد المستخدمين
        cursor.execute("SELECT COUNT(*) FROM users")
        user_count = cursor.fetchone()[0]
        print(f"📊 عدد المستخدمين المسجلين: {user_count}")
        
        # عرض المستخدمين (بدون كلمات المرور)
        if user_count > 0:
            cursor.execute("SELECT id, username, email, created_at FROM users")
            users = cursor.fetchall()
            print("👥 المستخدمون المسجلون:")
            for user in users:
                print(f"   - ID: {user[0]}, اسم المستخدم: {user[1]}, البريد: {user[2]}, تاريخ التسجيل: {user[3]}")
    else:
        print("❌ جدول المستخدمين غير موجود")
    
    conn.close()

def test_password_encryption():
    """اختبار تشفير كلمات المرور"""
    print("\n🔐 اختبار تشفير كلمات المرور...")
    
    test_password = "test123"
    
    # تشفير كلمة المرور
    hashed = bcrypt.hashpw(test_password.encode('utf-8'), bcrypt.gensalt())
    print("✅ تم تشفير كلمة المرور بنجاح")
    
    # التحقق من كلمة المرور
    if bcrypt.checkpw(test_password.encode('utf-8'), hashed):
        print("✅ التحقق من كلمة المرور يعمل بشكل صحيح")
    else:
        print("❌ خطأ في التحقق من كلمة المرور")

def test_remember_me():
    """اختبار وظيفة تذكرني"""
    print("\n💾 اختبار وظيفة تذكرني...")
    
    # إنشاء ملف تذكرني تجريبي
    remember_data = {
        "username": "test_user",
        "expires": (datetime.now() + timedelta(days=30)).isoformat()
    }
    
    with open("test_remember_me.json", "w", encoding="utf-8") as f:
        json.dump(remember_data, f, ensure_ascii=False)
    
    print("✅ تم إنشاء ملف تذكرني تجريبي")
    
    # قراءة الملف
    try:
        with open("test_remember_me.json", "r", encoding="utf-8") as f:
            data = json.load(f)
        
        expires = datetime.fromisoformat(data["expires"])
        if datetime.now() < expires:
            print(f"✅ ملف تذكرني صالح للمستخدم: {data['username']}")
        else:
            print("❌ ملف تذكرني منتهي الصلاحية")
    except Exception as e:
        print(f"❌ خطأ في قراءة ملف تذكرني: {e}")
    
    # حذف الملف التجريبي
    if os.path.exists("test_remember_me.json"):
        os.remove("test_remember_me.json")
        print("🗑️ تم حذف الملف التجريبي")

def test_imports():
    """اختبار استيراد المكتبات المطلوبة"""
    print("\n📦 اختبار المكتبات المطلوبة...")
    
    try:
        import customtkinter
        print("✅ customtkinter متاح")
    except ImportError:
        print("❌ customtkinter غير متاح")
    
    try:
        import tkinter
        print("✅ tkinter متاح")
    except ImportError:
        print("❌ tkinter غير متاح")
    
    try:
        import sqlite3
        print("✅ sqlite3 متاح")
    except ImportError:
        print("❌ sqlite3 غير متاح")
    
    try:
        import bcrypt
        print("✅ bcrypt متاح")
    except ImportError:
        print("❌ bcrypt غير متاح")

def main():
    """تشغيل جميع الاختبارات"""
    print("🧪 بدء اختبار نظام تسجيل الدخول")
    print("=" * 50)
    
    test_imports()
    test_database()
    test_password_encryption()
    test_remember_me()
    
    print("\n" + "=" * 50)
    print("✅ انتهت جميع الاختبارات")

if __name__ == "__main__":
    main()
