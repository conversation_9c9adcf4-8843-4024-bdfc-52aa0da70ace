# ◢◤ CYBER AUTH SYSTEM ◥◣

## الوصف
تطبيق سطح مكتب بتصميم سايبر فوتوريستك لنظام تسجيل الدخول مع واجهة مستخدم عصرية ومميزة بألوان النيون والتأثيرات السايبرية.

## 🚀 المميزات السايبرية
- ✅ **تصميم سايبر فوتوريستك** مع ألوان النيون الزاهية
- ✅ **واجهة Matrix-style** بخطوط Consolas وتأثيرات بصرية
- ✅ **تسجيل دخول آمن** مع رسائل نظام سايبرية
- ✅ **إنشاء حساب جديد** بتصميم عصري
- ✅ **استعادة كلمة المرور** مع واجهة أمنية متقدمة
- ✅ **خيار "تذكرني"** مع تشفير الجلسات
- ✅ **تشفير bcrypt** لحماية كلمات المرور
- ✅ **قاعدة بيانات محلية** SQLite آمنة
- ✅ **رسائل نظام تفاعلية** بأسلوب سايبر
- ✅ **ألوان نيون متدرجة**: أزرق، أخضر، بنفسجي
- ✅ **تحويل إلى EXE** مع أيقونة مخصصة

## متطلبات التشغيل
- Python 3.7 أو أحدث
- pip (مدير الحزم)

## طريقة التثبيت والتشغيل

### الطريقة الأولى: التشغيل المباشر
1. قم بتشغيل ملف `setup_and_run.bat`
2. سيتم تثبيت التبعيات المطلوبة تلقائياً
3. سيتم تشغيل البرنامج

### الطريقة الثانية: التثبيت اليدوي
```bash
# تثبيت التبعيات
pip install -r requirements.txt

# تشغيل البرنامج
python main.py
```

## إنشاء ملف exe
1. قم بتشغيل ملف `build_exe.bat`
2. انتظر حتى انتهاء عملية البناء
3. ستجد ملف exe في مجلد `dist`

## كيفية الاستخدام

### إنشاء حساب جديد
1. اضغط على "إنشاء حساب جديد"
2. املأ البيانات المطلوبة
3. اضغط "إنشاء الحساب"

### تسجيل الدخول
1. أدخل اسم المستخدم أو البريد الإلكتروني
2. أدخل كلمة المرور
3. اختر "تذكرني" إذا كنت تريد حفظ بيانات الدخول
4. اضغط "تسجيل الدخول"

### استعادة كلمة المرور
1. اضغط على "نسيت كلمة المرور؟"
2. أدخل بريدك الإلكتروني
3. اضغط "إرسال رابط الاستعادة"

## الملفات المهمة
- `main.py` - الملف الرئيسي للتطبيق
- `users.db` - قاعدة البيانات (تُنشأ تلقائياً)
- `remember_me.json` - ملف حفظ بيانات "تذكرني"
- `requirements.txt` - قائمة التبعيات المطلوبة

## الأمان
- كلمات المرور مشفرة باستخدام bcrypt
- بيانات "تذكرني" محفوظة محلياً مع انتهاء صلاحية
- قاعدة بيانات محلية آمنة

## المساعدة
إذا واجهت أي مشاكل، تأكد من:
1. تثبيت Python بشكل صحيح
2. تثبيت جميع التبعيات المطلوبة
3. تشغيل البرنامج من نفس المجلد
