# نظام تسجيل الدخول - AuthApp

## الوصف
تطبيق سطح مكتب عصري لنظام تسجيل الدخول مع واجهة مستخدم جميلة ومميزة.

## المميزات
- ✅ واجهة مستخدم عصرية ومميزة
- ✅ تسجيل الدخول
- ✅ إنشاء حساب جديد
- ✅ نسيت كلمة المرور
- ✅ خيار "تذكرني"
- ✅ تشفير كلمات المرور
- ✅ قاعدة بيانات محلية
- ✅ إمكانية تحويل إلى ملف exe

## متطلبات التشغيل
- Python 3.7 أو أحدث
- pip (مدير الحزم)

## طريقة التثبيت والتشغيل

### الطريقة الأولى: التشغيل المباشر
1. قم بتشغيل ملف `setup_and_run.bat`
2. سيتم تثبيت التبعيات المطلوبة تلقائياً
3. سيتم تشغيل البرنامج

### الطريقة الثانية: التثبيت اليدوي
```bash
# تثبيت التبعيات
pip install -r requirements.txt

# تشغيل البرنامج
python main.py
```

## إنشاء ملف exe
1. قم بتشغيل ملف `build_exe.bat`
2. انتظر حتى انتهاء عملية البناء
3. ستجد ملف exe في مجلد `dist`

## كيفية الاستخدام

### إنشاء حساب جديد
1. اضغط على "إنشاء حساب جديد"
2. املأ البيانات المطلوبة
3. اضغط "إنشاء الحساب"

### تسجيل الدخول
1. أدخل اسم المستخدم أو البريد الإلكتروني
2. أدخل كلمة المرور
3. اختر "تذكرني" إذا كنت تريد حفظ بيانات الدخول
4. اضغط "تسجيل الدخول"

### استعادة كلمة المرور
1. اضغط على "نسيت كلمة المرور؟"
2. أدخل بريدك الإلكتروني
3. اضغط "إرسال رابط الاستعادة"

## الملفات المهمة
- `main.py` - الملف الرئيسي للتطبيق
- `users.db` - قاعدة البيانات (تُنشأ تلقائياً)
- `remember_me.json` - ملف حفظ بيانات "تذكرني"
- `requirements.txt` - قائمة التبعيات المطلوبة

## الأمان
- كلمات المرور مشفرة باستخدام bcrypt
- بيانات "تذكرني" محفوظة محلياً مع انتهاء صلاحية
- قاعدة بيانات محلية آمنة

## المساعدة
إذا واجهت أي مشاكل، تأكد من:
1. تثبيت Python بشكل صحيح
2. تثبيت جميع التبعيات المطلوبة
3. تشغيل البرنامج من نفس المجلد
