"""
◢◤ CYBER SECURITY CENTER ◥◣
نظام الأمن السيبراني المتقدم
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox, ttk
import sqlite3
import bcrypt
import json
import os
import threading
import time
import psutil
import socket
import subprocess
import hashlib
from datetime import datetime, timedelta
import requests
import platform

# استيراد الوحدات
from dashboard_module import DashboardModule
from security_scanner_module import SecurityScannerModule
from network_monitor_module import NetworkMonitorModule
from learning_center_module import LearningCenterModule
from admin_panel_module import AdminPanelModule

# إعداد المظهر السايبر
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class CyberSecurityCenter:
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("◢◤ CYBER SECURITY CENTER ◥◣")
        self.root.geometry("1200x800")
        self.root.resizable(True, True)
        
        # ألوان سايبر مخصصة
        self.cyber_colors = {
            'neon_blue': '#00FFFF',
            'neon_green': '#00FF41',
            'neon_purple': '#BF00FF',
            'neon_red': '#FF0040',
            'dark_bg': '#0A0A0A',
            'cyber_gray': '#1A1A1A',
            'matrix_green': '#00FF00',
            'electric_blue': '#0080FF',
            'cyber_orange': '#FF8C00'
        }
        
        # متغيرات النظام
        self.current_user = None
        self.user_role = None
        self.current_page = "login"
        self.system_stats = {}
        self.network_monitoring = False
        self.security_scanning = False
        
        # إعداد قاعدة البيانات
        self.init_database()
        
        # إنشاء حساب الأدمن الافتراضي
        self.create_default_admin()
        
        # بدء واجهة تسجيل الدخول
        self.create_login_interface()
        
        # بدء مراقبة النظام
        self.start_system_monitoring()
    
    def init_database(self):
        """إنشاء قاعدة البيانات والجداول"""
        self.conn = sqlite3.connect('cyber_security.db')
        self.cursor = self.conn.cursor()
        
        # جدول المستخدمين
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                email TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                role TEXT DEFAULT 'user',
                permissions TEXT DEFAULT '[]',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP,
                is_active BOOLEAN DEFAULT 1
            )
        ''')
        
        # جدول سجلات النظام
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS system_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                action TEXT NOT NULL,
                details TEXT,
                ip_address TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # جدول فحص الأمان
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS security_scans (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                scan_type TEXT NOT NULL,
                results TEXT,
                threats_found INTEGER DEFAULT 0,
                scan_duration REAL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول مراقبة الشبكة
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS network_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                connection_type TEXT,
                local_address TEXT,
                remote_address TEXT,
                status TEXT,
                process_name TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        self.conn.commit()
    
    def create_default_admin(self):
        """إنشاء حساب الأدمن الافتراضي"""
        try:
            # التحقق من وجود الأدمن
            self.cursor.execute("SELECT id FROM users WHERE username = 'admin'")
            if not self.cursor.fetchone():
                # إنشاء حساب الأدمن
                hashed_password = bcrypt.hashpw("JaMaL@123".encode('utf-8'), bcrypt.gensalt())
                permissions = json.dumps([
                    "admin_panel", "user_management", "system_control", 
                    "security_scan", "network_monitor", "view_logs", 
                    "system_settings", "backup_restore"
                ])
                
                self.cursor.execute('''
                    INSERT INTO users (username, email, password, role, permissions)
                    VALUES (?, ?, ?, ?, ?)
                ''', ("admin", "<EMAIL>", hashed_password, "admin", permissions))
                
                self.conn.commit()
                print("[SYSTEM] Default admin account created successfully")
        except Exception as e:
            print(f"[ERROR] Failed to create admin account: {e}")
    
    def create_login_interface(self):
        """إنشاء واجهة تسجيل الدخول السايبرية"""
        # مسح الواجهة الحالية
        for widget in self.root.winfo_children():
            widget.destroy()
        
        # الإطار الرئيسي
        main_frame = ctk.CTkFrame(
            self.root,
            corner_radius=15,
            fg_color=("#1a1a1a", "#0d1117"),
            border_width=2,
            border_color=self.cyber_colors['neon_blue']
        )
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # العنوان الرئيسي
        title_label = ctk.CTkLabel(
            main_frame,
            text="◢◤ CYBER SECURITY CENTER ◥◣",
            font=ctk.CTkFont(size=36, weight="bold", family="Consolas"),
            text_color=self.cyber_colors['neon_blue']
        )
        title_label.pack(pady=(30, 10))
        
        # العنوان الفرعي
        subtitle_label = ctk.CTkLabel(
            main_frame,
            text="▶ SECURE ACCESS TERMINAL ◀",
            font=ctk.CTkFont(size=16, family="Consolas"),
            text_color=self.cyber_colors['neon_green']
        )
        subtitle_label.pack(pady=(0, 20))
        
        # خط فاصل
        line_frame = ctk.CTkFrame(main_frame, height=2, fg_color=self.cyber_colors['neon_blue'])
        line_frame.pack(fill="x", padx=100, pady=(0, 30))
        
        # رسالة النظام
        system_msg = ctk.CTkLabel(
            main_frame,
            text="[SYSTEM] Initializing secure authentication protocol...",
            font=ctk.CTkFont(size=12, family="Consolas"),
            text_color=self.cyber_colors['matrix_green']
        )
        system_msg.pack(pady=(0, 30))
        
        # إطار تسجيل الدخول
        login_frame = ctk.CTkFrame(
            main_frame,
            corner_radius=10,
            fg_color=("#2b2b2b", "#1a1a1a"),
            border_width=1,
            border_color=self.cyber_colors['neon_green']
        )
        login_frame.pack(pady=20, padx=200)
        
        # حقل اسم المستخدم
        username_label = ctk.CTkLabel(
            login_frame,
            text="┌─ USERNAME ─┐",
            font=ctk.CTkFont(size=12, family="Consolas"),
            text_color=self.cyber_colors['neon_blue']
        )
        username_label.pack(pady=(20, 5))
        
        self.username_entry = ctk.CTkEntry(
            login_frame,
            placeholder_text=">>> Enter your credentials...",
            width=300,
            height=40,
            font=ctk.CTkFont(size=12, family="Consolas"),
            fg_color=("#3b3b3b", "#2a2a2a"),
            border_color=self.cyber_colors['neon_blue'],
            border_width=2,
            text_color=self.cyber_colors['neon_green']
        )
        self.username_entry.pack(pady=(0, 15))
        
        # حقل كلمة المرور
        password_label = ctk.CTkLabel(
            login_frame,
            text="┌─ PASSWORD ─┐",
            font=ctk.CTkFont(size=12, family="Consolas"),
            text_color=self.cyber_colors['neon_blue']
        )
        password_label.pack(pady=(5, 5))
        
        self.password_entry = ctk.CTkEntry(
            login_frame,
            placeholder_text=">>> Enter secure key...",
            show="●",
            width=300,
            height=40,
            font=ctk.CTkFont(size=12, family="Consolas"),
            fg_color=("#3b3b3b", "#2a2a2a"),
            border_color=self.cyber_colors['neon_blue'],
            border_width=2,
            text_color=self.cyber_colors['neon_green']
        )
        self.password_entry.pack(pady=(0, 20))
        
        # زر تسجيل الدخول
        login_btn = ctk.CTkButton(
            login_frame,
            text="◢◤ INITIATE ACCESS ◥◣",
            width=300,
            height=50,
            font=ctk.CTkFont(size=16, weight="bold", family="Consolas"),
            fg_color=self.cyber_colors['neon_blue'],
            hover_color=self.cyber_colors['electric_blue'],
            text_color="black",
            border_width=2,
            border_color=self.cyber_colors['neon_green'],
            command=self.login
        )
        login_btn.pack(pady=(0, 30))
        
        # رسالة الحالة
        self.status_label = ctk.CTkLabel(
            main_frame,
            text="[STATUS] Ready for authentication...",
            font=ctk.CTkFont(size=10, family="Consolas"),
            text_color=self.cyber_colors['matrix_green']
        )
        self.status_label.pack(pady=(20, 10))
        
        # ربط Enter بتسجيل الدخول
        self.root.bind('<Return>', lambda event: self.login())
    
    def start_system_monitoring(self):
        """بدء مراقبة النظام في الخلفية"""
        def monitor():
            while True:
                try:
                    # جمع إحصائيات النظام
                    self.system_stats = {
                        'cpu_percent': psutil.cpu_percent(interval=1),
                        'memory_percent': psutil.virtual_memory().percent,
                        'disk_percent': psutil.disk_usage('/').percent if platform.system() != 'Windows' else psutil.disk_usage('C:').percent,
                        'network_connections': len(psutil.net_connections()),
                        'running_processes': len(psutil.pids()),
                        'boot_time': psutil.boot_time(),
                        'timestamp': datetime.now().isoformat()
                    }
                    time.sleep(5)  # تحديث كل 5 ثواني
                except Exception as e:
                    print(f"[ERROR] System monitoring error: {e}")
                    time.sleep(10)
        
        # تشغيل المراقبة في thread منفصل
        monitor_thread = threading.Thread(target=monitor, daemon=True)
        monitor_thread.start()

    def login(self):
        """تسجيل الدخول"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get()

        if not username or not password:
            self.show_cyber_message("⚠ INPUT ERROR", "[SYSTEM] All authentication fields required!", "error")
            return

        # البحث عن المستخدم
        self.cursor.execute(
            "SELECT id, username, password, role, permissions, is_active FROM users WHERE username = ?",
            (username,)
        )
        user = self.cursor.fetchone()

        if user and user[5] and bcrypt.checkpw(password.encode('utf-8'), user[2]):
            # تسجيل دخول ناجح
            self.current_user = {
                'id': user[0],
                'username': user[1],
                'role': user[3],
                'permissions': json.loads(user[4])
            }

            # تحديث آخر تسجيل دخول
            self.cursor.execute(
                "UPDATE users SET last_login = ? WHERE id = ?",
                (datetime.now().isoformat(), user[0])
            )

            # تسجيل العملية
            self.log_action("login", f"User {username} logged in successfully")

            self.conn.commit()
            self.create_main_interface()
        else:
            self.show_cyber_message("⚠ ACCESS DENIED", "[SECURITY] Invalid credentials or account disabled!", "error")

    def log_action(self, action, details="", ip_address="127.0.0.1"):
        """تسجيل العمليات في قاعدة البيانات"""
        try:
            user_id = self.current_user['id'] if self.current_user else None
            self.cursor.execute(
                "INSERT INTO system_logs (user_id, action, details, ip_address) VALUES (?, ?, ?, ?)",
                (user_id, action, details, ip_address)
            )
            self.conn.commit()
        except Exception as e:
            print(f"[ERROR] Failed to log action: {e}")

    def show_cyber_message(self, title, message, msg_type="info"):
        """عرض رسالة بتصميم سايبر"""
        if msg_type == "error":
            messagebox.showerror(title, message)
        elif msg_type == "success":
            messagebox.showinfo(title, message)
        elif msg_type == "warning":
            messagebox.showwarning(title, message)
        else:
            messagebox.showinfo(title, message)

    def create_main_interface(self):
        """إنشاء الواجهة الرئيسية"""
        # مسح الواجهة الحالية
        for widget in self.root.winfo_children():
            widget.destroy()

        # الإطار الرئيسي
        self.main_container = ctk.CTkFrame(
            self.root,
            corner_radius=0,
            fg_color=("#1a1a1a", "#0d1117")
        )
        self.main_container.pack(fill="both", expand=True)

        # إنشاء شريط القوائم العلوي
        self.create_top_menu()

        # إنشاء الشريط الجانبي
        self.create_sidebar()

        # إنشاء المنطقة الرئيسية للمحتوى
        self.create_content_area()

        # إنشاء الوحدات
        self.dashboard_module = DashboardModule(
            self.content_area, self.cyber_colors, self.system_stats, self.log_action
        )
        self.security_scanner_module = SecurityScannerModule(
            self.content_area, self.cyber_colors, self.log_action, self.cursor, self.conn
        )
        self.network_monitor_module = NetworkMonitorModule(
            self.content_area, self.cyber_colors, self.log_action, self.cursor, self.conn
        )
        self.learning_center_module = LearningCenterModule(
            self.content_area, self.cyber_colors, self.log_action, self.cursor, self.conn
        )

        # عرض لوحة التحكم الرئيسية
        self.show_dashboard()

    def create_top_menu(self):
        """إنشاء شريط القوائم العلوي"""
        self.top_menu = ctk.CTkFrame(
            self.main_container,
            height=60,
            corner_radius=0,
            fg_color=("#2b2b2b", "#1a1a1a"),
            border_width=2,
            border_color=self.cyber_colors['neon_blue']
        )
        self.top_menu.pack(fill="x", padx=0, pady=0)
        self.top_menu.pack_propagate(False)

        # العنوان
        title_label = ctk.CTkLabel(
            self.top_menu,
            text="◢◤ CYBER SECURITY CENTER ◥◣",
            font=ctk.CTkFont(size=20, weight="bold", family="Consolas"),
            text_color=self.cyber_colors['neon_blue']
        )
        title_label.pack(side="left", padx=20, pady=15)

        # معلومات المستخدم
        user_frame = ctk.CTkFrame(self.top_menu, fg_color="transparent")
        user_frame.pack(side="right", padx=20, pady=10)

        user_label = ctk.CTkLabel(
            user_frame,
            text=f"◉ {self.current_user['username'].upper()} | {self.current_user['role'].upper()}",
            font=ctk.CTkFont(size=12, family="Consolas"),
            text_color=self.cyber_colors['neon_green']
        )
        user_label.pack(side="left", padx=10)

        # زر تسجيل الخروج
        logout_btn = ctk.CTkButton(
            user_frame,
            text="⚠ LOGOUT",
            width=80,
            height=30,
            font=ctk.CTkFont(size=10, family="Consolas"),
            fg_color=self.cyber_colors['neon_red'],
            hover_color="#CC0000",
            text_color="white",
            command=self.logout
        )
        logout_btn.pack(side="right", padx=5)

    def create_sidebar(self):
        """إنشاء الشريط الجانبي للتنقل"""
        self.sidebar = ctk.CTkFrame(
            self.main_container,
            width=250,
            corner_radius=0,
            fg_color=("#2b2b2b", "#1a1a1a"),
            border_width=2,
            border_color=self.cyber_colors['neon_green']
        )
        self.sidebar.pack(side="left", fill="y", padx=0, pady=0)
        self.sidebar.pack_propagate(False)

        # عنوان القائمة
        menu_title = ctk.CTkLabel(
            self.sidebar,
            text="┌─ NAVIGATION MENU ─┐",
            font=ctk.CTkFont(size=14, weight="bold", family="Consolas"),
            text_color=self.cyber_colors['neon_green']
        )
        menu_title.pack(pady=(20, 10))

        # أزرار القوائم
        self.menu_buttons = {}

        # قائمة الوظائف الأساسية
        menu_items = [
            ("🏠 DASHBOARD", "dashboard", self.show_dashboard),
            ("🛡️ SECURITY SCANNER", "security", self.show_security_scanner),
            ("📡 NETWORK MONITOR", "network", self.show_network_monitor),
            ("🎓 LEARNING CENTER", "learning", self.show_learning_center),
            ("📊 SYSTEM REPORTS", "reports", self.show_system_reports),
            ("⚙️ SETTINGS", "settings", self.show_settings)
        ]

        # إضافة قائمة الأدمن إذا كان المستخدم أدمن
        if self.current_user['role'] == 'admin':
            menu_items.append(("👑 ADMIN PANEL", "admin", self.show_admin_panel))

        for text, key, command in menu_items:
            btn = ctk.CTkButton(
                self.sidebar,
                text=text,
                width=220,
                height=40,
                font=ctk.CTkFont(size=12, family="Consolas"),
                fg_color="transparent",
                border_width=1,
                border_color=self.cyber_colors['neon_blue'],
                text_color=self.cyber_colors['neon_blue'],
                hover_color=("#3b3b3b", "#2a2a2a"),
                command=command
            )
            btn.pack(pady=5, padx=15)
            self.menu_buttons[key] = btn

        # معلومات النظام في أسفل الشريط الجانبي
        system_info_frame = ctk.CTkFrame(
            self.sidebar,
            fg_color="transparent"
        )
        system_info_frame.pack(side="bottom", fill="x", padx=10, pady=20)

        # حالة النظام
        self.system_status = ctk.CTkLabel(
            system_info_frame,
            text="● SYSTEM ONLINE",
            font=ctk.CTkFont(size=10, family="Consolas"),
            text_color=self.cyber_colors['matrix_green']
        )
        self.system_status.pack(pady=2)

        # الوقت الحالي
        self.current_time = ctk.CTkLabel(
            system_info_frame,
            text="",
            font=ctk.CTkFont(size=9, family="Consolas"),
            text_color=self.cyber_colors['neon_blue']
        )
        self.current_time.pack(pady=2)

        # تحديث الوقت
        self.update_time()

    def create_content_area(self):
        """إنشاء المنطقة الرئيسية للمحتوى"""
        self.content_area = ctk.CTkFrame(
            self.main_container,
            corner_radius=0,
            fg_color=("#1a1a1a", "#0d1117")
        )
        self.content_area.pack(side="right", fill="both", expand=True, padx=0, pady=0)

    def update_time(self):
        """تحديث الوقت الحالي"""
        try:
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.current_time.configure(text=f"[TIME] {current_time}")
            self.root.after(1000, self.update_time)  # تحديث كل ثانية
        except:
            pass

    def show_dashboard(self):
        """عرض لوحة التحكم الرئيسية"""
        self.current_page = "dashboard"
        self.highlight_menu_button("dashboard")
        self.dashboard_module.create_dashboard()
        self.log_action("page_view", "Viewed dashboard")

    def show_security_scanner(self):
        """عرض فحص الأمان"""
        self.current_page = "security"
        self.highlight_menu_button("security")
        self.security_scanner_module.create_security_scanner()
        self.log_action("page_view", "Viewed security scanner")

    def show_network_monitor(self):
        """عرض مراقب الشبكة"""
        self.current_page = "network"
        self.highlight_menu_button("network")
        self.create_network_monitor()
        self.log_action("page_view", "Viewed network monitor")

    def show_learning_center(self):
        """عرض مركز التعليم"""
        self.current_page = "learning"
        self.highlight_menu_button("learning")
        self.create_learning_center()
        self.log_action("page_view", "Viewed learning center")

    def show_system_reports(self):
        """عرض تقارير النظام"""
        self.current_page = "reports"
        self.highlight_menu_button("reports")
        self.create_system_reports()
        self.log_action("page_view", "Viewed system reports")

    def show_settings(self):
        """عرض الإعدادات"""
        self.current_page = "settings"
        self.highlight_menu_button("settings")
        self.create_settings()
        self.log_action("page_view", "Viewed settings")

    def show_admin_panel(self):
        """عرض لوحة الأدمن"""
        if self.current_user['role'] != 'admin':
            self.show_cyber_message("⚠ ACCESS DENIED", "[SECURITY] Admin privileges required!", "error")
            return

        self.current_page = "admin"
        self.highlight_menu_button("admin")
        self.create_admin_panel()
        self.log_action("page_view", "Viewed admin panel")

    def highlight_menu_button(self, active_button):
        """تمييز الزر النشط في القائمة"""
        for key, button in self.menu_buttons.items():
            if key == active_button:
                button.configure(
                    fg_color=self.cyber_colors['neon_blue'],
                    text_color="black"
                )
            else:
                button.configure(
                    fg_color="transparent",
                    text_color=self.cyber_colors['neon_blue']
                )

    def create_network_monitor(self):
        """إنشاء مراقب الشبكة"""
        self.network_monitor_module.create_network_monitor()

    def create_learning_center(self):
        """إنشاء مركز التعليم"""
        self.learning_center_module.create_learning_center()

    def create_system_reports(self):
        """إنشاء تقارير النظام"""
        # مسح المحتوى السابق
        for widget in self.content_area.winfo_children():
            widget.destroy()

        # عنوان مؤقت
        title_label = ctk.CTkLabel(
            self.content_area,
            text="◢◤ SYSTEM REPORTS ◥◣\n[UNDER DEVELOPMENT]",
            font=ctk.CTkFont(size=24, weight="bold", family="Consolas"),
            text_color=self.cyber_colors['neon_purple']
        )
        title_label.pack(expand=True)

    def create_settings(self):
        """إنشاء الإعدادات"""
        # مسح المحتوى السابق
        for widget in self.content_area.winfo_children():
            widget.destroy()

        # عنوان مؤقت
        title_label = ctk.CTkLabel(
            self.content_area,
            text="◢◤ SYSTEM SETTINGS ◥◣\n[UNDER DEVELOPMENT]",
            font=ctk.CTkFont(size=24, weight="bold", family="Consolas"),
            text_color=self.cyber_colors['neon_orange']
        )
        title_label.pack(expand=True)

    def create_admin_panel(self):
        """إنشاء لوحة الأدمن"""
        # مسح المحتوى السابق
        for widget in self.content_area.winfo_children():
            widget.destroy()

        # عنوان مؤقت
        title_label = ctk.CTkLabel(
            self.content_area,
            text="◢◤ ADMIN CONTROL PANEL ◥◣\n[UNDER DEVELOPMENT]",
            font=ctk.CTkFont(size=24, weight="bold", family="Consolas"),
            text_color=self.cyber_colors['neon_red']
        )
        title_label.pack(expand=True)

    def logout(self):
        """تسجيل الخروج"""
        if self.current_user:
            self.log_action("logout", f"User {self.current_user['username']} logged out")

        self.current_user = None
        self.user_role = None
        self.current_page = "login"
        self.create_login_interface()

    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

if __name__ == "__main__":
    app = CyberSecurityCenter()
    app.run()
