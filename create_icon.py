"""
ملف لإنشاء أيقونة بسيطة للبرنامج
يمكن تشغيله لإنشاء ملف icon.ico
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_icon():
    # إنشاء صورة 256x256
    size = 256
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # رسم دائرة خلفية
    margin = 20
    draw.ellipse([margin, margin, size-margin, size-margin], 
                fill=(52, 152, 219, 255), outline=(41, 128, 185, 255), width=4)
    
    # رسم رمز المستخدم (دائرة صغيرة للرأس)
    head_size = 40
    head_x = size // 2 - head_size // 2
    head_y = size // 2 - 30
    draw.ellipse([head_x, head_y, head_x + head_size, head_y + head_size], 
                fill=(255, 255, 255, 255))
    
    # رسم الجسم (نصف دائرة)
    body_width = 80
    body_height = 50
    body_x = size // 2 - body_width // 2
    body_y = head_y + head_size + 10
    draw.ellipse([body_x, body_y, body_x + body_width, body_y + body_height * 2], 
                fill=(255, 255, 255, 255))
    
    # حفظ الأيقونة
    img.save('icon.ico', format='ICO', sizes=[(256, 256), (128, 128), (64, 64), (32, 32), (16, 16)])
    print("تم إنشاء ملف icon.ico بنجاح!")

if __name__ == "__main__":
    try:
        create_icon()
    except ImportError:
        print("يرجى تثبيت مكتبة Pillow أولاً:")
        print("pip install Pillow")
    except Exception as e:
        print(f"خطأ في إنشاء الأيقونة: {e}")
