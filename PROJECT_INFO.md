# 🚀 نظام تسجيل الدخول - AuthApp

## 📋 نظرة عامة
تطبيق سطح مكتب عصري ومميز لنظام تسجيل الدخول مع واجهة مستخدم جميلة وآمنة.

## 🎯 المميزات الرئيسية
- ✅ **واجهة عصرية**: تصميم حديث باستخدام CustomTkinter
- ✅ **أمان عالي**: تشفير كلمات المرور باستخدام bcrypt
- ✅ **قاعدة بيانات محلية**: SQLite لحفظ بيانات المستخدمين
- ✅ **تسجيل دخول**: نظام آمن لتسجيل الدخول
- ✅ **إنشاء حساب**: تسجيل مستخدمين جدد
- ✅ **استعادة كلمة المرور**: نظام لاستعادة كلمات المرور المنسية
- ✅ **تذكرني**: حفظ بيانات الدخول لمدة 30 يوم
- ✅ **تحويل إلى EXE**: إمكانية إنشاء ملف تنفيذي مستقل

## 📁 هيكل المشروع
```
Jo/
├── main.py                 # الملف الرئيسي للتطبيق
├── requirements.txt        # قائمة التبعيات المطلوبة
├── AuthApp.spec           # ملف تكوين PyInstaller
├── setup_and_run.bat     # تثبيت التبعيات وتشغيل البرنامج
├── run_app.bat           # تشغيل البرنامج فقط
├── build_exe.bat         # إنشاء ملف exe
├── test_app.py           # ملف اختبار الوظائف
├── create_icon.py        # إنشاء أيقونة للبرنامج
├── README.md             # دليل المستخدم التفصيلي
├── QUICK_START.txt       # دليل البدء السريع
├── PROJECT_INFO.md       # معلومات المشروع (هذا الملف)
├── users.db              # قاعدة البيانات (تُنشأ تلقائياً)
└── remember_me.json      # ملف حفظ بيانات "تذكرني"
```

## 🛠️ التقنيات المستخدمة
- **Python 3.7+**: لغة البرمجة الأساسية
- **CustomTkinter**: للواجهة الرسومية العصرية
- **SQLite**: قاعدة البيانات المحلية
- **bcrypt**: تشفير كلمات المرور
- **PyInstaller**: تحويل إلى ملف exe
- **JSON**: حفظ إعدادات "تذكرني"

## 🚀 طرق التشغيل

### 1. التشغيل السريع
```bash
# انقر نقراً مزدوجاً على:
setup_and_run.bat
```

### 2. التشغيل اليدوي
```bash
python -m pip install -r requirements.txt
python main.py
```

### 3. إنشاء ملف EXE
```bash
# انقر نقراً مزدوجاً على:
build_exe.bat
```

## 🧪 اختبار البرنامج
```bash
python test_app.py
```

## 🔒 الأمان
- **تشفير كلمات المرور**: باستخدام bcrypt مع salt عشوائي
- **قاعدة بيانات محلية**: لا توجد اتصالات خارجية
- **انتهاء صلاحية "تذكرني"**: 30 يوم كحد أقصى
- **التحقق من صحة البيانات**: فحص شامل للمدخلات

## 📱 واجهات المستخدم
1. **شاشة تسجيل الدخول**: الواجهة الرئيسية
2. **شاشة إنشاء حساب**: تسجيل مستخدمين جدد
3. **شاشة استعادة كلمة المرور**: لاستعادة كلمات المرور المنسية
4. **شاشة لوحة التحكم**: بعد تسجيل الدخول بنجاح

## 🎨 التخصيص
- **المظهر**: يمكن تغيير المظهر من dark إلى light
- **الألوان**: يمكن تغيير نظام الألوان (blue, green, dark-blue)
- **الخطوط**: قابلة للتخصيص في الكود
- **الأحجام**: يمكن تعديل أحجام النوافذ والعناصر

## 📊 إحصائيات المشروع
- **عدد الملفات**: 11 ملف
- **أسطر الكود**: ~500 سطر
- **المكتبات المستخدمة**: 3 مكتبات رئيسية
- **الوظائف**: 15+ وظيفة رئيسية

## 🔧 استكشاف الأخطاء
1. **خطأ في تثبيت المكتبات**: تأكد من تثبيت Python بشكل صحيح
2. **خطأ في تشغيل البرنامج**: شغل كمدير إذا لزم الأمر
3. **مشاكل في قاعدة البيانات**: احذف ملف users.db وأعد التشغيل
4. **مشاكل في "تذكرني"**: احذف ملف remember_me.json

## 📈 التطوير المستقبلي
- إضافة نظام إرسال البريد الإلكتروني الفعلي
- إضافة المزيد من خيارات الأمان (2FA)
- إضافة نظام الأدوار والصلاحيات
- إضافة قاعدة بيانات خارجية
- إضافة واجهة ويب

## 📞 الدعم
- اقرأ ملف README.md للتفاصيل الكاملة
- شغل test_app.py للتأكد من سلامة النظام
- راجع ملف QUICK_START.txt للبدء السريع

---
**تم إنشاء هذا المشروع بواسطة Augment Agent** 🤖
