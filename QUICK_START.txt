🚀 دليل البدء السريع - ◢◤ CYBER AUTH SYSTEM ◥◣

📋 الخطوات:

1️⃣ تشغيل البرنامج مباشرة:
   - انقر نقراً مزدوجاً على "setup_and_run.bat"
   - أو انقر على "run_app.bat" إذا كانت التبعيات مثبتة
   - أو انقر على "run_cyber_demo.bat" لعرض التأثيرات السايبرية

2️⃣ إنشاء ملف exe:
   - انقر نقراً مزدوجاً على "build_exe.bat"
   - انتظر حتى انتهاء العملية
   - ستجد ملف "AuthApp.exe" في مجلد "dist"

🎯 المميزات السايبرية:
✅ تصميم سايبر فوتوريستك مع ألوان النيون
✅ واجهة Matrix-style بخطوط Consolas
✅ تسجيل دخول آمن مع رسائل نظام سايبرية
✅ إنشاء حساب جديد بتصميم عصري
✅ استعادة كلمة المرور مع واجهة أمنية
✅ خيار "تذكرني" مع تشفير الجلسات
✅ تشفير bcrypt لحماية كلمات المرور
✅ قاعدة بيانات محلية آمنة
✅ رسائل نظام تفاعلية بأسلوب سايبر
✅ ألوان نيون متدرجة (أزرق، أخضر، بنفسجي)

📁 الملفات المهمة:
- main.py: الكود الرئيسي
- users.db: قاعدة البيانات (تُنشأ تلقائياً)
- remember_me.json: بيانات "تذكرني"

🔧 في حالة المشاكل:
1. تأكد من تثبيت Python
2. شغل "setup_and_run.bat" كمدير
3. اقرأ ملف README.md للتفاصيل

💡 نصائح:
- استخدم كلمة مرور قوية (6 أحرف على الأقل)
- خيار "تذكرني" يحفظ البيانات لمدة 30 يوم
- يمكن تشغيل عدة نسخ من البرنامج
