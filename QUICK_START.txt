🚀 دليل البدء السريع - نظام تسجيل الدخول

📋 الخطوات:

1️⃣ تشغيل البرنامج مباشرة:
   - انقر نقراً مزدوجاً على "setup_and_run.bat"
   - أو انقر على "run_app.bat" إذا كانت التبعيات مثبتة

2️⃣ إنشاء ملف exe:
   - انقر نقراً مزدوجاً على "build_exe.bat"
   - انتظر حتى انتهاء العملية
   - ستجد ملف "AuthApp.exe" في مجلد "dist"

🎯 المميزات:
✅ واجهة عصرية ومميزة
✅ تسجيل دخول آمن
✅ إنشاء حساب جديد
✅ استعادة كلمة المرور
✅ خيار "تذكرني"
✅ تشفير كلمات المرور
✅ قاعدة بيانات محلية

📁 الملفات المهمة:
- main.py: الكود الرئيسي
- users.db: قاعدة البيانات (تُنشأ تلقائياً)
- remember_me.json: بيانات "تذكرني"

🔧 في حالة المشاكل:
1. تأكد من تثبيت Python
2. شغل "setup_and_run.bat" كمدير
3. اقرأ ملف README.md للتفاصيل

💡 نصائح:
- استخدم كلمة مرور قوية (6 أحرف على الأقل)
- خيار "تذكرني" يحفظ البيانات لمدة 30 يوم
- يمكن تشغيل عدة نسخ من البرنامج
