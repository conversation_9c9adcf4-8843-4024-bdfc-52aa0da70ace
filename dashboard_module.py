"""
وحدة لوحة التحكم الرئيسية
Dashboard Module for Cyber Security Center
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import ttk
from datetime import datetime, timedelta
import threading
import time

# محاولة استيراد psutil، إذا لم يكن متاحاً استخدم بيانات وهمية
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    print("[WARNING] psutil not available, using mock data")

# محاولة استيراد matplotlib، إذا لم يكن متاحاً تجاهل الرسوم البيانية
try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
    import matplotlib.dates as mdates
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("[WARNING] matplotlib not available, charts disabled")

class DashboardModule:
    def __init__(self, parent, cyber_colors, system_stats, log_action):
        self.parent = parent
        self.cyber_colors = cyber_colors
        self.system_stats = system_stats
        self.log_action = log_action
        self.monitoring_active = True
        
    def create_dashboard(self):
        """إنشاء لوحة التحكم الرئيسية"""
        # مسح المحتوى السابق
        for widget in self.parent.winfo_children():
            widget.destroy()
        
        # إطار التمرير الرئيسي
        main_scroll = ctk.CTkScrollableFrame(
            self.parent,
            corner_radius=0,
            fg_color=("#1a1a1a", "#0d1117")
        )
        main_scroll.pack(fill="both", expand=True, padx=10, pady=10)
        
        # العنوان الرئيسي
        title_frame = ctk.CTkFrame(
            main_scroll,
            height=80,
            corner_radius=10,
            fg_color=("#2b2b2b", "#1a1a1a"),
            border_width=2,
            border_color=self.cyber_colors['neon_blue']
        )
        title_frame.pack(fill="x", pady=(0, 20))
        title_frame.pack_propagate(False)
        
        title_label = ctk.CTkLabel(
            title_frame,
            text="◢◤ SYSTEM DASHBOARD ◥◣",
            font=ctk.CTkFont(size=28, weight="bold", family="Consolas"),
            text_color=self.cyber_colors['neon_blue']
        )
        title_label.pack(pady=20)
        
        # الصف الأول - إحصائيات النظام
        stats_frame = ctk.CTkFrame(main_scroll, fg_color="transparent")
        stats_frame.pack(fill="x", pady=(0, 20))
        
        # إحصائيات المعالج
        self.cpu_card = self.create_stat_card(
            stats_frame, "CPU USAGE", "0%", self.cyber_colors['neon_red']
        )
        self.cpu_card.pack(side="left", fill="both", expand=True, padx=(0, 10))
        
        # إحصائيات الذاكرة
        self.memory_card = self.create_stat_card(
            stats_frame, "MEMORY USAGE", "0%", self.cyber_colors['neon_orange']
        )
        self.memory_card.pack(side="left", fill="both", expand=True, padx=5)
        
        # إحصائيات القرص الصلب
        self.disk_card = self.create_stat_card(
            stats_frame, "DISK USAGE", "0%", self.cyber_colors['neon_purple']
        )
        self.disk_card.pack(side="left", fill="both", expand=True, padx=5)
        
        # إحصائيات الشبكة
        self.network_card = self.create_stat_card(
            stats_frame, "NETWORK CONNECTIONS", "0", self.cyber_colors['neon_green']
        )
        self.network_card.pack(side="left", fill="both", expand=True, padx=(10, 0))
        
        # الصف الثاني - الرسوم البيانية
        charts_frame = ctk.CTkFrame(main_scroll, fg_color="transparent")
        charts_frame.pack(fill="both", expand=True, pady=(0, 20))
        
        # رسم بياني لاستخدام المعالج
        self.cpu_chart_frame = ctk.CTkFrame(
            charts_frame,
            corner_radius=10,
            fg_color=("#2b2b2b", "#1a1a1a"),
            border_width=1,
            border_color=self.cyber_colors['neon_blue']
        )
        self.cpu_chart_frame.pack(side="left", fill="both", expand=True, padx=(0, 10))
        
        # رسم بياني لاستخدام الذاكرة
        self.memory_chart_frame = ctk.CTkFrame(
            charts_frame,
            corner_radius=10,
            fg_color=("#2b2b2b", "#1a1a1a"),
            border_width=1,
            border_color=self.cyber_colors['neon_green']
        )
        self.memory_chart_frame.pack(side="right", fill="both", expand=True, padx=(10, 0))
        
        # الصف الثالث - معلومات النظام والتهديدات
        info_frame = ctk.CTkFrame(main_scroll, fg_color="transparent")
        info_frame.pack(fill="x", pady=(0, 20))
        
        # معلومات النظام
        self.system_info_frame = ctk.CTkFrame(
            info_frame,
            corner_radius=10,
            fg_color=("#2b2b2b", "#1a1a1a"),
            border_width=1,
            border_color=self.cyber_colors['neon_blue']
        )
        self.system_info_frame.pack(side="left", fill="both", expand=True, padx=(0, 10))
        
        # تحديثات الأمان
        self.security_updates_frame = ctk.CTkFrame(
            info_frame,
            corner_radius=10,
            fg_color=("#2b2b2b", "#1a1a1a"),
            border_width=1,
            border_color=self.cyber_colors['neon_red']
        )
        self.security_updates_frame.pack(side="right", fill="both", expand=True, padx=(10, 0))
        
        # إنشاء محتوى الأقسام
        self.create_system_info()
        self.create_security_updates()

        # إنشاء الرسوم البيانية إذا كان matplotlib متاحاً
        if MATPLOTLIB_AVAILABLE:
            self.create_charts()
        else:
            self.create_simple_charts()

        # بدء تحديث البيانات
        self.start_data_updates()
    
    def create_stat_card(self, parent, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card = ctk.CTkFrame(
            parent,
            height=120,
            corner_radius=10,
            fg_color=("#2b2b2b", "#1a1a1a"),
            border_width=2,
            border_color=color
        )
        card.pack_propagate(False)
        
        # العنوان
        title_label = ctk.CTkLabel(
            card,
            text=title,
            font=ctk.CTkFont(size=12, family="Consolas"),
            text_color=color
        )
        title_label.pack(pady=(15, 5))
        
        # القيمة
        value_label = ctk.CTkLabel(
            card,
            text=value,
            font=ctk.CTkFont(size=24, weight="bold", family="Consolas"),
            text_color=color
        )
        value_label.pack(pady=(0, 10))
        
        # حفظ مرجع للتحديث
        card.value_label = value_label
        
        return card
    
    def create_system_info(self):
        """إنشاء قسم معلومات النظام"""
        # العنوان
        title = ctk.CTkLabel(
            self.system_info_frame,
            text="┌─ SYSTEM INFORMATION ─┐",
            font=ctk.CTkFont(size=14, weight="bold", family="Consolas"),
            text_color=self.cyber_colors['neon_blue']
        )
        title.pack(pady=(15, 10))
        
        # معلومات النظام
        import platform
        system_info = [
            f"OS: {platform.system()} {platform.release()}",
            f"Architecture: {platform.architecture()[0]}",
            f"Processor: {platform.processor()[:30]}...",
            f"Python Version: {platform.python_version()}"
        ]

        # إضافة معلومات إضافية إذا كان psutil متاحاً
        if PSUTIL_AVAILABLE:
            try:
                boot_time = datetime.fromtimestamp(psutil.boot_time()).strftime('%Y-%m-%d %H:%M:%S')
                system_info.append(f"Boot Time: {boot_time}")
            except:
                system_info.append("Boot Time: N/A")
        else:
            system_info.append("Boot Time: N/A (psutil required)")
        
        for info in system_info:
            info_label = ctk.CTkLabel(
                self.system_info_frame,
                text=info,
                font=ctk.CTkFont(size=10, family="Consolas"),
                text_color=self.cyber_colors['neon_green']
            )
            info_label.pack(pady=2, padx=15, anchor="w")
    
    def create_security_updates(self):
        """إنشاء قسم تحديثات الأمان"""
        # العنوان
        title = ctk.CTkLabel(
            self.security_updates_frame,
            text="┌─ SECURITY STATUS ─┐",
            font=ctk.CTkFont(size=14, weight="bold", family="Consolas"),
            text_color=self.cyber_colors['neon_red']
        )
        title.pack(pady=(15, 10))
        
        # حالة الأمان
        security_status = [
            "✓ Firewall: ACTIVE",
            "✓ Antivirus: UPDATED",
            "⚠ System Updates: 3 PENDING",
            "✓ Network Monitor: RUNNING",
            "✓ Threat Detection: ENABLED"
        ]
        
        for status in security_status:
            color = self.cyber_colors['neon_green'] if status.startswith('✓') else self.cyber_colors['neon_orange']
            status_label = ctk.CTkLabel(
                self.security_updates_frame,
                text=status,
                font=ctk.CTkFont(size=10, family="Consolas"),
                text_color=color
            )
            status_label.pack(pady=2, padx=15, anchor="w")
    
    def create_charts(self):
        """إنشاء الرسوم البيانية"""
        # رسم بياني للمعالج
        self.create_cpu_chart()
        
        # رسم بياني للذاكرة
        self.create_memory_chart()
    
    def create_cpu_chart(self):
        """إنشاء رسم بياني لاستخدام المعالج"""
        # العنوان
        title = ctk.CTkLabel(
            self.cpu_chart_frame,
            text="CPU USAGE HISTORY",
            font=ctk.CTkFont(size=12, weight="bold", family="Consolas"),
            text_color=self.cyber_colors['neon_blue']
        )
        title.pack(pady=(10, 5))
        
        # إنشاء الرسم البياني
        fig, ax = plt.subplots(figsize=(6, 3), facecolor='#1a1a1a')
        ax.set_facecolor('#1a1a1a')
        
        # بيانات تجريبية
        times = [datetime.now() - timedelta(minutes=i) for i in range(30, 0, -1)]
        cpu_data = [psutil.cpu_percent() for _ in range(30)]
        
        ax.plot(times, cpu_data, color='#00FFFF', linewidth=2)
        ax.fill_between(times, cpu_data, alpha=0.3, color='#00FFFF')
        
        ax.set_ylabel('CPU %', color='#00FFFF', fontsize=8)
        ax.set_xlabel('Time', color='#00FFFF', fontsize=8)
        ax.tick_params(colors='#00FFFF', labelsize=6)
        ax.grid(True, alpha=0.3, color='#00FFFF')
        
        # تنسيق التواريخ
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
        plt.xticks(rotation=45)
        plt.tight_layout()
        
        # إضافة الرسم البياني للواجهة
        canvas = FigureCanvasTkAgg(fig, self.cpu_chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill="both", expand=True, padx=10, pady=(0, 10))
    
    def create_memory_chart(self):
        """إنشاء رسم بياني لاستخدام الذاكرة"""
        # العنوان
        title = ctk.CTkLabel(
            self.memory_chart_frame,
            text="MEMORY USAGE HISTORY",
            font=ctk.CTkFont(size=12, weight="bold", family="Consolas"),
            text_color=self.cyber_colors['neon_green']
        )
        title.pack(pady=(10, 5))
        
        # إنشاء الرسم البياني
        fig, ax = plt.subplots(figsize=(6, 3), facecolor='#1a1a1a')
        ax.set_facecolor('#1a1a1a')
        
        # بيانات تجريبية
        times = [datetime.now() - timedelta(minutes=i) for i in range(30, 0, -1)]
        memory_data = [psutil.virtual_memory().percent for _ in range(30)]
        
        ax.plot(times, memory_data, color='#00FF41', linewidth=2)
        ax.fill_between(times, memory_data, alpha=0.3, color='#00FF41')
        
        ax.set_ylabel('Memory %', color='#00FF41', fontsize=8)
        ax.set_xlabel('Time', color='#00FF41', fontsize=8)
        ax.tick_params(colors='#00FF41', labelsize=6)
        ax.grid(True, alpha=0.3, color='#00FF41')
        
        # تنسيق التواريخ
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
        plt.xticks(rotation=45)
        plt.tight_layout()
        
        # إضافة الرسم البياني للواجهة
        canvas = FigureCanvasTkAgg(fig, self.memory_chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill="both", expand=True, padx=10, pady=(0, 10))

    def create_simple_charts(self):
        """إنشاء رسوم بيانية بسيطة بدون matplotlib"""
        # رسم بياني بسيط للمعالج
        cpu_title = ctk.CTkLabel(
            self.cpu_chart_frame,
            text="CPU USAGE HISTORY",
            font=ctk.CTkFont(size=12, weight="bold", family="Consolas"),
            text_color=self.cyber_colors['neon_blue']
        )
        cpu_title.pack(pady=(10, 5))

        cpu_info = ctk.CTkLabel(
            self.cpu_chart_frame,
            text="[CHART] Real-time CPU monitoring\n(Install matplotlib for advanced charts)",
            font=ctk.CTkFont(size=10, family="Consolas"),
            text_color=self.cyber_colors['neon_green']
        )
        cpu_info.pack(expand=True)

        # رسم بياني بسيط للذاكرة
        memory_title = ctk.CTkLabel(
            self.memory_chart_frame,
            text="MEMORY USAGE HISTORY",
            font=ctk.CTkFont(size=12, weight="bold", family="Consolas"),
            text_color=self.cyber_colors['neon_green']
        )
        memory_title.pack(pady=(10, 5))

        memory_info = ctk.CTkLabel(
            self.memory_chart_frame,
            text="[CHART] Real-time Memory monitoring\n(Install matplotlib for advanced charts)",
            font=ctk.CTkFont(size=10, family="Consolas"),
            text_color=self.cyber_colors['neon_blue']
        )
        memory_info.pack(expand=True)
    
    def start_data_updates(self):
        """بدء تحديث البيانات"""
        def update_stats():
            while self.monitoring_active:
                try:
                    if PSUTIL_AVAILABLE:
                        # تحديث إحصائيات المعالج
                        cpu_percent = psutil.cpu_percent(interval=1)
                        self.cpu_card.value_label.configure(text=f"{cpu_percent:.1f}%")

                        # تحديث إحصائيات الذاكرة
                        memory_percent = psutil.virtual_memory().percent
                        self.memory_card.value_label.configure(text=f"{memory_percent:.1f}%")

                        # تحديث إحصائيات القرص
                        import platform
                        try:
                            if platform.system() == 'Windows':
                                disk_percent = psutil.disk_usage('C:').percent
                            else:
                                disk_percent = psutil.disk_usage('/').percent
                            self.disk_card.value_label.configure(text=f"{disk_percent:.1f}%")
                        except:
                            self.disk_card.value_label.configure(text="N/A")

                        # تحديث اتصالات الشبكة
                        try:
                            network_connections = len(psutil.net_connections())
                            self.network_card.value_label.configure(text=str(network_connections))
                        except:
                            self.network_card.value_label.configure(text="N/A")
                    else:
                        # استخدام بيانات وهمية إذا لم يكن psutil متاحاً
                        import random
                        self.cpu_card.value_label.configure(text=f"{random.randint(10, 80):.1f}%")
                        self.memory_card.value_label.configure(text=f"{random.randint(30, 70):.1f}%")
                        self.disk_card.value_label.configure(text=f"{random.randint(40, 90):.1f}%")
                        self.network_card.value_label.configure(text=str(random.randint(5, 50)))

                    time.sleep(2)  # تحديث كل ثانيتين
                except Exception as e:
                    print(f"[ERROR] Stats update error: {e}")
                    time.sleep(5)
        
        # تشغيل التحديث في thread منفصل
        update_thread = threading.Thread(target=update_stats, daemon=True)
        update_thread.start()
    
    def stop_monitoring(self):
        """إيقاف المراقبة"""
        self.monitoring_active = False
