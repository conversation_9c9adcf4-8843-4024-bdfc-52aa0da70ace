import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox
import sqlite3
import bcrypt
import json
import os
from datetime import datetime, timedelta

# إعداد المظهر العام - سايبر
ctk.set_appearance_mode("dark")  # مظهر مظلم للطابع السايبر
ctk.set_default_color_theme("blue")  # ألوان زرقاء نيون

class AuthApp:
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("◢◤ CYBER AUTH SYSTEM ◥◣")
        self.root.geometry("450x650")
        self.root.resizable(False, False)

        # ألوان سايبر مخصصة
        self.cyber_colors = {
            'neon_blue': '#00FFFF',
            'neon_green': '#00FF41',
            'neon_purple': '#BF00FF',
            'dark_bg': '#0A0A0A',
            'cyber_gray': '#1A1A1A',
            'matrix_green': '#00FF00',
            'electric_blue': '#0080FF'
        }
        
        # إعداد قاعدة البيانات
        self.init_database()
        
        # متغيرات الواجهة
        self.remember_me_var = ctk.BooleanVar()
        
        # إنشاء الواجهة الرئيسية
        self.create_login_interface()
        
        # تحميل بيانات "تذكرني" إن وجدت
        self.load_remember_me()
        
    def init_database(self):
        """إنشاء قاعدة البيانات وجدول المستخدمين"""
        self.conn = sqlite3.connect('users.db')
        self.cursor = self.conn.cursor()
        
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                email TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                reset_token TEXT,
                reset_token_expires TIMESTAMP
            )
        ''')
        self.conn.commit()
    
    def create_login_interface(self):
        """إنشاء واجهة تسجيل الدخول - تصميم سايبر"""
        # مسح الواجهة الحالية
        for widget in self.root.winfo_children():
            widget.destroy()

        # الإطار الرئيسي مع تأثير سايبر
        main_frame = ctk.CTkFrame(
            self.root,
            corner_radius=15,
            fg_color=("#1a1a1a", "#0d1117"),
            border_width=2,
            border_color=self.cyber_colors['neon_blue']
        )
        main_frame.pack(fill="both", expand=True, padx=15, pady=15)

        # العنوان الرئيسي - سايبر
        title_label = ctk.CTkLabel(
            main_frame,
            text="◢◤ CYBER LOGIN ◥◣",
            font=ctk.CTkFont(size=32, weight="bold", family="Consolas"),
            text_color=self.cyber_colors['neon_blue']
        )
        title_label.pack(pady=(25, 5))

        # خط تحت العنوان
        line_frame = ctk.CTkFrame(main_frame, height=2, fg_color=self.cyber_colors['neon_blue'])
        line_frame.pack(fill="x", padx=80, pady=(0, 10))

        # العنوان الفرعي
        subtitle_label = ctk.CTkLabel(
            main_frame,
            text="▶ ACCESS TERMINAL REQUIRED ◀",
            font=ctk.CTkFont(size=14, family="Consolas"),
            text_color=self.cyber_colors['neon_green']
        )
        subtitle_label.pack(pady=(5, 25))

        # رسالة النظام
        system_msg = ctk.CTkLabel(
            main_frame,
            text="[SYSTEM] Initializing secure connection...",
            font=ctk.CTkFont(size=10, family="Consolas"),
            text_color=self.cyber_colors['matrix_green']
        )
        system_msg.pack(pady=(0, 15))
        
        # حقل اسم المستخدم - سايبر
        username_label = ctk.CTkLabel(
            main_frame,
            text="┌─ USERNAME/EMAIL ─┐",
            font=ctk.CTkFont(size=11, family="Consolas"),
            text_color=self.cyber_colors['neon_blue']
        )
        username_label.pack(pady=(5, 2))

        self.username_entry = ctk.CTkEntry(
            main_frame,
            placeholder_text=">>> Enter your credentials...",
            width=320,
            height=45,
            font=ctk.CTkFont(size=12, family="Consolas"),
            fg_color=("#2b2b2b", "#1a1a1a"),
            border_color=self.cyber_colors['neon_blue'],
            border_width=2,
            text_color=self.cyber_colors['neon_green']
        )
        self.username_entry.pack(pady=(0, 15))

        # حقل كلمة المرور - سايبر
        password_label = ctk.CTkLabel(
            main_frame,
            text="┌─ PASSWORD ─┐",
            font=ctk.CTkFont(size=11, family="Consolas"),
            text_color=self.cyber_colors['neon_blue']
        )
        password_label.pack(pady=(5, 2))

        self.password_entry = ctk.CTkEntry(
            main_frame,
            placeholder_text=">>> Enter secure key...",
            show="●",
            width=320,
            height=45,
            font=ctk.CTkFont(size=12, family="Consolas"),
            fg_color=("#2b2b2b", "#1a1a1a"),
            border_color=self.cyber_colors['neon_blue'],
            border_width=2,
            text_color=self.cyber_colors['neon_green']
        )
        self.password_entry.pack(pady=(0, 15))
        
        # خيار تذكرني - سايبر
        remember_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        remember_frame.pack(pady=15)

        self.remember_checkbox = ctk.CTkCheckBox(
            remember_frame,
            text="◉ REMEMBER SESSION",
            variable=self.remember_me_var,
            font=ctk.CTkFont(size=11, family="Consolas"),
            text_color=self.cyber_colors['neon_green'],
            checkmark_color=self.cyber_colors['neon_blue'],
            border_color=self.cyber_colors['neon_blue']
        )
        self.remember_checkbox.pack(side="left")

        # رابط نسيت كلمة المرور - سايبر
        forgot_password_btn = ctk.CTkButton(
            remember_frame,
            text="⚠ FORGOT ACCESS KEY?",
            fg_color="transparent",
            text_color=self.cyber_colors['neon_purple'],
            hover_color=("#2b2b2b", "#1a1a1a"),
            width=140,
            height=25,
            font=ctk.CTkFont(size=10, family="Consolas"),
            command=self.show_forgot_password,
            border_width=1,
            border_color=self.cyber_colors['neon_purple']
        )
        forgot_password_btn.pack(side="right")
        
        # زر تسجيل الدخول - سايبر
        login_btn = ctk.CTkButton(
            main_frame,
            text="◢◤ INITIATE LOGIN ◥◣",
            width=320,
            height=50,
            font=ctk.CTkFont(size=16, weight="bold", family="Consolas"),
            fg_color=self.cyber_colors['neon_blue'],
            hover_color=self.cyber_colors['electric_blue'],
            text_color="black",
            border_width=2,
            border_color=self.cyber_colors['neon_green'],
            command=self.login
        )
        login_btn.pack(pady=20)

        # خط فاصل سايبر
        separator_label = ctk.CTkLabel(
            main_frame,
            text="═══════════════════════════════════",
            font=ctk.CTkFont(size=12, family="Consolas"),
            text_color=self.cyber_colors['neon_blue']
        )
        separator_label.pack(pady=15)

        # زر إنشاء حساب جديد - سايبر
        register_btn = ctk.CTkButton(
            main_frame,
            text="⚡ CREATE NEW ACCOUNT ⚡",
            width=320,
            height=45,
            font=ctk.CTkFont(size=14, family="Consolas"),
            fg_color="transparent",
            border_width=2,
            border_color=self.cyber_colors['neon_green'],
            text_color=self.cyber_colors['neon_green'],
            hover_color=("#2b2b2b", "#1a1a1a"),
            command=self.show_register_interface
        )
        register_btn.pack(pady=10)

        # زر الخروج - سايبر
        exit_btn = ctk.CTkButton(
            main_frame,
            text="⚠ TERMINATE SESSION",
            width=150,
            height=35,
            font=ctk.CTkFont(size=11, family="Consolas"),
            fg_color=self.cyber_colors['neon_purple'],
            hover_color="#8B008B",
            text_color="white",
            command=self.exit_app
        )
        exit_btn.pack(pady=(15, 10))

        # رسالة حالة النظام
        status_label = ctk.CTkLabel(
            main_frame,
            text="[STATUS] Ready for authentication...",
            font=ctk.CTkFont(size=9, family="Consolas"),
            text_color=self.cyber_colors['matrix_green']
        )
        status_label.pack(pady=(10, 5))
    
    def create_register_interface(self):
        """إنشاء واجهة التسجيل الجديد - تصميم سايبر"""
        # مسح الواجهة الحالية
        for widget in self.root.winfo_children():
            widget.destroy()

        # الإطار الرئيسي مع تأثير سايبر
        main_frame = ctk.CTkFrame(
            self.root,
            corner_radius=15,
            fg_color=("#1a1a1a", "#0d1117"),
            border_width=2,
            border_color=self.cyber_colors['neon_green']
        )
        main_frame.pack(fill="both", expand=True, padx=15, pady=15)

        # العنوان - سايبر
        title_label = ctk.CTkLabel(
            main_frame,
            text="◢◤ NEW USER REGISTRATION ◥◣",
            font=ctk.CTkFont(size=26, weight="bold", family="Consolas"),
            text_color=self.cyber_colors['neon_green']
        )
        title_label.pack(pady=(25, 15))

        # خط تحت العنوان
        line_frame = ctk.CTkFrame(main_frame, height=2, fg_color=self.cyber_colors['neon_green'])
        line_frame.pack(fill="x", padx=80, pady=(0, 20))
        
        # حقل اسم المستخدم
        self.reg_username_entry = ctk.CTkEntry(
            main_frame,
            placeholder_text="اسم المستخدم",
            width=300,
            height=40,
            font=ctk.CTkFont(size=12)
        )
        self.reg_username_entry.pack(pady=10)
        
        # حقل البريد الإلكتروني
        self.reg_email_entry = ctk.CTkEntry(
            main_frame,
            placeholder_text="البريد الإلكتروني",
            width=300,
            height=40,
            font=ctk.CTkFont(size=12)
        )
        self.reg_email_entry.pack(pady=10)
        
        # حقل كلمة المرور
        self.reg_password_entry = ctk.CTkEntry(
            main_frame,
            placeholder_text="كلمة المرور",
            show="*",
            width=300,
            height=40,
            font=ctk.CTkFont(size=12)
        )
        self.reg_password_entry.pack(pady=10)
        
        # حقل تأكيد كلمة المرور
        self.reg_confirm_password_entry = ctk.CTkEntry(
            main_frame,
            placeholder_text="تأكيد كلمة المرور",
            show="*",
            width=300,
            height=40,
            font=ctk.CTkFont(size=12)
        )
        self.reg_confirm_password_entry.pack(pady=10)
        
        # زر إنشاء الحساب
        register_btn = ctk.CTkButton(
            main_frame,
            text="إنشاء الحساب",
            width=300,
            height=40,
            font=ctk.CTkFont(size=14, weight="bold"),
            command=self.register
        )
        register_btn.pack(pady=20)
        
        # زر العودة لتسجيل الدخول
        back_btn = ctk.CTkButton(
            main_frame,
            text="العودة لتسجيل الدخول",
            width=200,
            height=35,
            font=ctk.CTkFont(size=12),
            fg_color="transparent",
            border_width=2,
            text_color=("gray10", "gray90"),
            command=self.create_login_interface
        )
        back_btn.pack(pady=10)

    def create_forgot_password_interface(self):
        """إنشاء واجهة نسيت كلمة المرور - تصميم سايبر"""
        # مسح الواجهة الحالية
        for widget in self.root.winfo_children():
            widget.destroy()

        # الإطار الرئيسي مع تأثير سايبر
        main_frame = ctk.CTkFrame(
            self.root,
            corner_radius=15,
            fg_color=("#1a1a1a", "#0d1117"),
            border_width=2,
            border_color=self.cyber_colors['neon_purple']
        )
        main_frame.pack(fill="both", expand=True, padx=15, pady=15)

        # العنوان - سايبر
        title_label = ctk.CTkLabel(
            main_frame,
            text="◢◤ PASSWORD RECOVERY ◥◣",
            font=ctk.CTkFont(size=26, weight="bold", family="Consolas"),
            text_color=self.cyber_colors['neon_purple']
        )
        title_label.pack(pady=(30, 15))

        # خط تحت العنوان
        line_frame = ctk.CTkFrame(main_frame, height=2, fg_color=self.cyber_colors['neon_purple'])
        line_frame.pack(fill="x", padx=80, pady=(0, 15))

        # وصف - سايبر
        desc_label = ctk.CTkLabel(
            main_frame,
            text="⚠ ENTER EMAIL FOR SECURITY RESET ⚠",
            font=ctk.CTkFont(size=12, family="Consolas"),
            text_color=self.cyber_colors['neon_green']
        )
        desc_label.pack(pady=(0, 25))

        # حقل البريد الإلكتروني
        self.forgot_email_entry = ctk.CTkEntry(
            main_frame,
            placeholder_text="البريد الإلكتروني",
            width=300,
            height=40,
            font=ctk.CTkFont(size=12)
        )
        self.forgot_email_entry.pack(pady=10)

        # زر إرسال رابط الاستعادة
        reset_btn = ctk.CTkButton(
            main_frame,
            text="إرسال رابط الاستعادة",
            width=300,
            height=40,
            font=ctk.CTkFont(size=14, weight="bold"),
            command=self.send_reset_link
        )
        reset_btn.pack(pady=20)

        # زر العودة
        back_btn = ctk.CTkButton(
            main_frame,
            text="العودة",
            width=150,
            height=35,
            font=ctk.CTkFont(size=12),
            fg_color="transparent",
            border_width=2,
            text_color=("gray10", "gray90"),
            command=self.create_login_interface
        )
        back_btn.pack(pady=10)

    def create_dashboard_interface(self, username):
        """إنشاء واجهة لوحة التحكم بعد تسجيل الدخول - تصميم سايبر"""
        # مسح الواجهة الحالية
        for widget in self.root.winfo_children():
            widget.destroy()

        # الإطار الرئيسي مع تأثير سايبر
        main_frame = ctk.CTkFrame(
            self.root,
            corner_radius=15,
            fg_color=("#1a1a1a", "#0d1117"),
            border_width=2,
            border_color=self.cyber_colors['matrix_green']
        )
        main_frame.pack(fill="both", expand=True, padx=15, pady=15)

        # رسالة الترحيب - سايبر
        welcome_label = ctk.CTkLabel(
            main_frame,
            text=f"◢◤ WELCOME {username.upper()} ◥◣",
            font=ctk.CTkFont(size=28, weight="bold", family="Consolas"),
            text_color=self.cyber_colors['matrix_green']
        )
        welcome_label.pack(pady=(40, 15))

        # خط تحت الترحيب
        line_frame = ctk.CTkFrame(main_frame, height=2, fg_color=self.cyber_colors['matrix_green'])
        line_frame.pack(fill="x", padx=80, pady=(0, 15))

        success_label = ctk.CTkLabel(
            main_frame,
            text="✓ ACCESS GRANTED - LOGIN SUCCESSFUL",
            font=ctk.CTkFont(size=16, family="Consolas"),
            text_color=self.cyber_colors['neon_green']
        )
        success_label.pack(pady=10)

        # رسالة حالة النظام
        status_label = ctk.CTkLabel(
            main_frame,
            text="[SYSTEM] Connection established. All systems operational.",
            font=ctk.CTkFont(size=11, family="Consolas"),
            text_color=self.cyber_colors['neon_blue']
        )
        status_label.pack(pady=15)

        # معلومات المستخدم
        info_frame = ctk.CTkFrame(main_frame)
        info_frame.pack(pady=30, padx=20, fill="x")

        info_label = ctk.CTkLabel(
            info_frame,
            text="معلومات الحساب",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        info_label.pack(pady=20)

        # هنا يمكن إضافة المزيد من المعلومات والوظائف

        # زر تسجيل الخروج - سايبر
        logout_btn = ctk.CTkButton(
            main_frame,
            text="◢◤ TERMINATE SESSION ◥◣",
            width=250,
            height=45,
            font=ctk.CTkFont(size=14, weight="bold", family="Consolas"),
            fg_color=self.cyber_colors['neon_purple'],
            hover_color="#8B008B",
            text_color="white",
            border_width=2,
            border_color=self.cyber_colors['neon_blue'],
            command=self.logout
        )
        logout_btn.pack(pady=40)

        # رسالة أمان
        security_label = ctk.CTkLabel(
            main_frame,
            text="[SECURITY] Session will be encrypted and stored securely.",
            font=ctk.CTkFont(size=9, family="Consolas"),
            text_color=self.cyber_colors['matrix_green']
        )
        security_label.pack(pady=(10, 5))

    def login(self):
        """تسجيل الدخول"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get()

        if not username or not password:
            messagebox.showerror("⚠ CYBER ERROR", "[SYSTEM] All fields must be completed for authentication!")
            return

        # البحث عن المستخدم في قاعدة البيانات
        self.cursor.execute(
            "SELECT username, password FROM users WHERE username = ? OR email = ?",
            (username, username)
        )
        user = self.cursor.fetchone()

        if user and bcrypt.checkpw(password.encode('utf-8'), user[1]):
            # تسجيل دخول ناجح
            if self.remember_me_var.get():
                self.save_remember_me(username)
            else:
                self.clear_remember_me()

            self.create_dashboard_interface(user[0])
        else:
            messagebox.showerror("⚠ ACCESS DENIED", "[SECURITY] Invalid credentials detected! Authentication failed.")

    def register(self):
        """تسجيل مستخدم جديد"""
        username = self.reg_username_entry.get().strip()
        email = self.reg_email_entry.get().strip()
        password = self.reg_password_entry.get()
        confirm_password = self.reg_confirm_password_entry.get()

        # التحقق من صحة البيانات - سايبر
        if not all([username, email, password, confirm_password]):
            messagebox.showerror("⚠ REGISTRATION ERROR", "[SYSTEM] All data fields required for new user creation!")
            return

        if password != confirm_password:
            messagebox.showerror("⚠ PASSWORD MISMATCH", "[SECURITY] Password confirmation does not match!")
            return

        if len(password) < 6:
            messagebox.showerror("⚠ WEAK PASSWORD", "[SECURITY] Password must be minimum 6 characters for cyber protection!")
            return

        # تشفير كلمة المرور
        hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())

        try:
            # إدراج المستخدم الجديد
            self.cursor.execute(
                "INSERT INTO users (username, email, password) VALUES (?, ?, ?)",
                (username, email, hashed_password)
            )
            self.conn.commit()

            messagebox.showinfo("✓ REGISTRATION SUCCESS", "[SYSTEM] New cyber account created successfully! Welcome to the matrix.")
            self.create_login_interface()

        except sqlite3.IntegrityError:
            messagebox.showerror("⚠ DUPLICATE DATA", "[DATABASE] Username or email already exists in the system!")

    def send_reset_link(self):
        """إرسال رابط استعادة كلمة المرور"""
        email = self.forgot_email_entry.get().strip()

        if not email:
            messagebox.showerror("⚠ INPUT ERROR", "[SYSTEM] Email address required for password recovery!")
            return

        # البحث عن المستخدم
        self.cursor.execute("SELECT id FROM users WHERE email = ?", (email,))
        user = self.cursor.fetchone()

        if user:
            # في التطبيق الحقيقي، ستقوم بإرسال بريد إلكتروني
            # هنا سنعرض رسالة تأكيد فقط
            messagebox.showinfo(
                "✓ RECOVERY INITIATED",
                "[SYSTEM] Secure recovery link transmitted to your email. Check your inbox for further instructions."
            )
        else:
            messagebox.showerror("⚠ EMAIL NOT FOUND", "[DATABASE] Email address not registered in the cyber system!")

    def logout(self):
        """تسجيل الخروج"""
        self.clear_remember_me()
        self.create_login_interface()

    def save_remember_me(self, username):
        """حفظ بيانات تذكرني"""
        remember_data = {
            "username": username,
            "expires": (datetime.now() + timedelta(days=30)).isoformat()
        }
        with open("remember_me.json", "w", encoding="utf-8") as f:
            json.dump(remember_data, f, ensure_ascii=False)

    def load_remember_me(self):
        """تحميل بيانات تذكرني"""
        try:
            if os.path.exists("remember_me.json"):
                with open("remember_me.json", "r", encoding="utf-8") as f:
                    data = json.load(f)

                expires = datetime.fromisoformat(data["expires"])
                if datetime.now() < expires:
                    self.username_entry.insert(0, data["username"])
                    self.remember_me_var.set(True)
                else:
                    self.clear_remember_me()
        except:
            pass

    def clear_remember_me(self):
        """مسح بيانات تذكرني"""
        if os.path.exists("remember_me.json"):
            os.remove("remember_me.json")

    def show_register_interface(self):
        """عرض واجهة التسجيل"""
        self.create_register_interface()

    def show_forgot_password(self):
        """عرض واجهة نسيت كلمة المرور"""
        self.create_forgot_password_interface()

    def exit_app(self):
        """إغلاق التطبيق"""
        self.conn.close()
        self.root.quit()

    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

if __name__ == "__main__":
    app = AuthApp()
    app.run()
