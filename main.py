import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox
import sqlite3
import bcrypt
import json
import os
from datetime import datetime, timedelta

# إعداد المظهر العام
ctk.set_appearance_mode("dark")  # "dark" أو "light"
ctk.set_default_color_theme("blue")  # "blue", "green", "dark-blue"

class AuthApp:
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("نظام تسجيل الدخول")
        self.root.geometry("400x600")
        self.root.resizable(False, False)
        
        # إعداد قاعدة البيانات
        self.init_database()
        
        # متغيرات الواجهة
        self.remember_me_var = ctk.BooleanVar()
        
        # إنشاء الواجهة الرئيسية
        self.create_login_interface()
        
        # تحميل بيانات "تذكرني" إن وجدت
        self.load_remember_me()
        
    def init_database(self):
        """إنشاء قاعدة البيانات وجدول المستخدمين"""
        self.conn = sqlite3.connect('users.db')
        self.cursor = self.conn.cursor()
        
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                email TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                reset_token TEXT,
                reset_token_expires TIMESTAMP
            )
        ''')
        self.conn.commit()
    
    def create_login_interface(self):
        """إنشاء واجهة تسجيل الدخول"""
        # مسح الواجهة الحالية
        for widget in self.root.winfo_children():
            widget.destroy()
        
        # الإطار الرئيسي
        main_frame = ctk.CTkFrame(self.root, corner_radius=20)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # العنوان
        title_label = ctk.CTkLabel(
            main_frame, 
            text="مرحباً بك", 
            font=ctk.CTkFont(size=28, weight="bold")
        )
        title_label.pack(pady=(30, 10))
        
        subtitle_label = ctk.CTkLabel(
            main_frame, 
            text="سجل دخولك للمتابعة", 
            font=ctk.CTkFont(size=14)
        )
        subtitle_label.pack(pady=(0, 30))
        
        # حقل اسم المستخدم
        self.username_entry = ctk.CTkEntry(
            main_frame,
            placeholder_text="اسم المستخدم أو البريد الإلكتروني",
            width=300,
            height=40,
            font=ctk.CTkFont(size=12)
        )
        self.username_entry.pack(pady=10)
        
        # حقل كلمة المرور
        self.password_entry = ctk.CTkEntry(
            main_frame,
            placeholder_text="كلمة المرور",
            show="*",
            width=300,
            height=40,
            font=ctk.CTkFont(size=12)
        )
        self.password_entry.pack(pady=10)
        
        # خيار تذكرني
        remember_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        remember_frame.pack(pady=10)
        
        self.remember_checkbox = ctk.CTkCheckBox(
            remember_frame,
            text="تذكرني",
            variable=self.remember_me_var,
            font=ctk.CTkFont(size=12)
        )
        self.remember_checkbox.pack(side="left")
        
        # رابط نسيت كلمة المرور
        forgot_password_btn = ctk.CTkButton(
            remember_frame,
            text="نسيت كلمة المرور؟",
            fg_color="transparent",
            text_color=("gray10", "gray90"),
            hover_color=("gray80", "gray20"),
            width=120,
            height=20,
            font=ctk.CTkFont(size=11),
            command=self.show_forgot_password
        )
        forgot_password_btn.pack(side="right")
        
        # زر تسجيل الدخول
        login_btn = ctk.CTkButton(
            main_frame,
            text="تسجيل الدخول",
            width=300,
            height=40,
            font=ctk.CTkFont(size=14, weight="bold"),
            command=self.login
        )
        login_btn.pack(pady=20)
        
        # خط فاصل
        separator_frame = ctk.CTkFrame(main_frame, height=2, fg_color=("gray80", "gray20"))
        separator_frame.pack(fill="x", padx=50, pady=20)
        
        # زر إنشاء حساب جديد
        register_btn = ctk.CTkButton(
            main_frame,
            text="إنشاء حساب جديد",
            width=300,
            height=40,
            font=ctk.CTkFont(size=14),
            fg_color="transparent",
            border_width=2,
            text_color=("gray10", "gray90"),
            command=self.show_register_interface
        )
        register_btn.pack(pady=10)
        
        # زر الخروج
        exit_btn = ctk.CTkButton(
            main_frame,
            text="خروج",
            width=100,
            height=30,
            font=ctk.CTkFont(size=12),
            fg_color="red",
            hover_color="darkred",
            command=self.exit_app
        )
        exit_btn.pack(pady=(20, 10))
    
    def create_register_interface(self):
        """إنشاء واجهة التسجيل الجديد"""
        # مسح الواجهة الحالية
        for widget in self.root.winfo_children():
            widget.destroy()
        
        # الإطار الرئيسي
        main_frame = ctk.CTkFrame(self.root, corner_radius=20)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # العنوان
        title_label = ctk.CTkLabel(
            main_frame, 
            text="إنشاء حساب جديد", 
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(30, 30))
        
        # حقل اسم المستخدم
        self.reg_username_entry = ctk.CTkEntry(
            main_frame,
            placeholder_text="اسم المستخدم",
            width=300,
            height=40,
            font=ctk.CTkFont(size=12)
        )
        self.reg_username_entry.pack(pady=10)
        
        # حقل البريد الإلكتروني
        self.reg_email_entry = ctk.CTkEntry(
            main_frame,
            placeholder_text="البريد الإلكتروني",
            width=300,
            height=40,
            font=ctk.CTkFont(size=12)
        )
        self.reg_email_entry.pack(pady=10)
        
        # حقل كلمة المرور
        self.reg_password_entry = ctk.CTkEntry(
            main_frame,
            placeholder_text="كلمة المرور",
            show="*",
            width=300,
            height=40,
            font=ctk.CTkFont(size=12)
        )
        self.reg_password_entry.pack(pady=10)
        
        # حقل تأكيد كلمة المرور
        self.reg_confirm_password_entry = ctk.CTkEntry(
            main_frame,
            placeholder_text="تأكيد كلمة المرور",
            show="*",
            width=300,
            height=40,
            font=ctk.CTkFont(size=12)
        )
        self.reg_confirm_password_entry.pack(pady=10)
        
        # زر إنشاء الحساب
        register_btn = ctk.CTkButton(
            main_frame,
            text="إنشاء الحساب",
            width=300,
            height=40,
            font=ctk.CTkFont(size=14, weight="bold"),
            command=self.register
        )
        register_btn.pack(pady=20)
        
        # زر العودة لتسجيل الدخول
        back_btn = ctk.CTkButton(
            main_frame,
            text="العودة لتسجيل الدخول",
            width=200,
            height=35,
            font=ctk.CTkFont(size=12),
            fg_color="transparent",
            border_width=2,
            text_color=("gray10", "gray90"),
            command=self.create_login_interface
        )
        back_btn.pack(pady=10)

    def create_forgot_password_interface(self):
        """إنشاء واجهة نسيت كلمة المرور"""
        # مسح الواجهة الحالية
        for widget in self.root.winfo_children():
            widget.destroy()

        # الإطار الرئيسي
        main_frame = ctk.CTkFrame(self.root, corner_radius=20)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # العنوان
        title_label = ctk.CTkLabel(
            main_frame,
            text="استعادة كلمة المرور",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(30, 20))

        # وصف
        desc_label = ctk.CTkLabel(
            main_frame,
            text="أدخل بريدك الإلكتروني لاستعادة كلمة المرور",
            font=ctk.CTkFont(size=12),
            text_color=("gray10", "gray70")
        )
        desc_label.pack(pady=(0, 30))

        # حقل البريد الإلكتروني
        self.forgot_email_entry = ctk.CTkEntry(
            main_frame,
            placeholder_text="البريد الإلكتروني",
            width=300,
            height=40,
            font=ctk.CTkFont(size=12)
        )
        self.forgot_email_entry.pack(pady=10)

        # زر إرسال رابط الاستعادة
        reset_btn = ctk.CTkButton(
            main_frame,
            text="إرسال رابط الاستعادة",
            width=300,
            height=40,
            font=ctk.CTkFont(size=14, weight="bold"),
            command=self.send_reset_link
        )
        reset_btn.pack(pady=20)

        # زر العودة
        back_btn = ctk.CTkButton(
            main_frame,
            text="العودة",
            width=150,
            height=35,
            font=ctk.CTkFont(size=12),
            fg_color="transparent",
            border_width=2,
            text_color=("gray10", "gray90"),
            command=self.create_login_interface
        )
        back_btn.pack(pady=10)

    def create_dashboard_interface(self, username):
        """إنشاء واجهة لوحة التحكم بعد تسجيل الدخول"""
        # مسح الواجهة الحالية
        for widget in self.root.winfo_children():
            widget.destroy()

        # الإطار الرئيسي
        main_frame = ctk.CTkFrame(self.root, corner_radius=20)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # رسالة الترحيب
        welcome_label = ctk.CTkLabel(
            main_frame,
            text=f"مرحباً {username}!",
            font=ctk.CTkFont(size=28, weight="bold")
        )
        welcome_label.pack(pady=(50, 20))

        success_label = ctk.CTkLabel(
            main_frame,
            text="تم تسجيل الدخول بنجاح",
            font=ctk.CTkFont(size=16),
            text_color="green"
        )
        success_label.pack(pady=10)

        # معلومات المستخدم
        info_frame = ctk.CTkFrame(main_frame)
        info_frame.pack(pady=30, padx=20, fill="x")

        info_label = ctk.CTkLabel(
            info_frame,
            text="معلومات الحساب",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        info_label.pack(pady=20)

        # هنا يمكن إضافة المزيد من المعلومات والوظائف

        # زر تسجيل الخروج
        logout_btn = ctk.CTkButton(
            main_frame,
            text="تسجيل الخروج",
            width=200,
            height=40,
            font=ctk.CTkFont(size=14),
            fg_color="red",
            hover_color="darkred",
            command=self.logout
        )
        logout_btn.pack(pady=30)

    def login(self):
        """تسجيل الدخول"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get()

        if not username or not password:
            messagebox.showerror("خطأ", "يرجى ملء جميع الحقول")
            return

        # البحث عن المستخدم في قاعدة البيانات
        self.cursor.execute(
            "SELECT username, password FROM users WHERE username = ? OR email = ?",
            (username, username)
        )
        user = self.cursor.fetchone()

        if user and bcrypt.checkpw(password.encode('utf-8'), user[1]):
            # تسجيل دخول ناجح
            if self.remember_me_var.get():
                self.save_remember_me(username)
            else:
                self.clear_remember_me()

            self.create_dashboard_interface(user[0])
        else:
            messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")

    def register(self):
        """تسجيل مستخدم جديد"""
        username = self.reg_username_entry.get().strip()
        email = self.reg_email_entry.get().strip()
        password = self.reg_password_entry.get()
        confirm_password = self.reg_confirm_password_entry.get()

        # التحقق من صحة البيانات
        if not all([username, email, password, confirm_password]):
            messagebox.showerror("خطأ", "يرجى ملء جميع الحقول")
            return

        if password != confirm_password:
            messagebox.showerror("خطأ", "كلمات المرور غير متطابقة")
            return

        if len(password) < 6:
            messagebox.showerror("خطأ", "كلمة المرور يجب أن تكون 6 أحرف على الأقل")
            return

        # تشفير كلمة المرور
        hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())

        try:
            # إدراج المستخدم الجديد
            self.cursor.execute(
                "INSERT INTO users (username, email, password) VALUES (?, ?, ?)",
                (username, email, hashed_password)
            )
            self.conn.commit()

            messagebox.showinfo("نجح", "تم إنشاء الحساب بنجاح!")
            self.create_login_interface()

        except sqlite3.IntegrityError:
            messagebox.showerror("خطأ", "اسم المستخدم أو البريد الإلكتروني مستخدم بالفعل")

    def send_reset_link(self):
        """إرسال رابط استعادة كلمة المرور"""
        email = self.forgot_email_entry.get().strip()

        if not email:
            messagebox.showerror("خطأ", "يرجى إدخال البريد الإلكتروني")
            return

        # البحث عن المستخدم
        self.cursor.execute("SELECT id FROM users WHERE email = ?", (email,))
        user = self.cursor.fetchone()

        if user:
            # في التطبيق الحقيقي، ستقوم بإرسال بريد إلكتروني
            # هنا سنعرض رسالة تأكيد فقط
            messagebox.showinfo(
                "تم الإرسال",
                "تم إرسال رابط استعادة كلمة المرور إلى بريدك الإلكتروني"
            )
        else:
            messagebox.showerror("خطأ", "البريد الإلكتروني غير مسجل")

    def logout(self):
        """تسجيل الخروج"""
        self.clear_remember_me()
        self.create_login_interface()

    def save_remember_me(self, username):
        """حفظ بيانات تذكرني"""
        remember_data = {
            "username": username,
            "expires": (datetime.now() + timedelta(days=30)).isoformat()
        }
        with open("remember_me.json", "w", encoding="utf-8") as f:
            json.dump(remember_data, f, ensure_ascii=False)

    def load_remember_me(self):
        """تحميل بيانات تذكرني"""
        try:
            if os.path.exists("remember_me.json"):
                with open("remember_me.json", "r", encoding="utf-8") as f:
                    data = json.load(f)

                expires = datetime.fromisoformat(data["expires"])
                if datetime.now() < expires:
                    self.username_entry.insert(0, data["username"])
                    self.remember_me_var.set(True)
                else:
                    self.clear_remember_me()
        except:
            pass

    def clear_remember_me(self):
        """مسح بيانات تذكرني"""
        if os.path.exists("remember_me.json"):
            os.remove("remember_me.json")

    def show_register_interface(self):
        """عرض واجهة التسجيل"""
        self.create_register_interface()

    def show_forgot_password(self):
        """عرض واجهة نسيت كلمة المرور"""
        self.create_forgot_password_interface()

    def exit_app(self):
        """إغلاق التطبيق"""
        self.conn.close()
        self.root.quit()

    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

if __name__ == "__main__":
    app = AuthApp()
    app.run()
