"""
وحدة لوحة الأدمن
Admin Panel Module for Cyber Security Center
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import sqlite3
import bcrypt
import json
from datetime import datetime
import threading

class AdminPanelModule:
    def __init__(self, parent, cyber_colors, log_action, cursor, conn, current_user):
        self.parent = parent
        self.cyber_colors = cyber_colors
        self.log_action = log_action
        self.cursor = cursor
        self.conn = conn
        self.current_user = current_user
        
    def create_admin_panel(self):
        """إنشاء لوحة الأدمن"""
        # مسح المحتوى السابق
        for widget in self.parent.winfo_children():
            widget.destroy()
        
        # إطار التمرير الرئيسي
        main_scroll = ctk.CTkScrollableFrame(
            self.parent,
            corner_radius=0,
            fg_color=("#1a1a1a", "#0d1117")
        )
        main_scroll.pack(fill="both", expand=True, padx=10, pady=10)
        
        # العنوان الرئيسي
        title_frame = ctk.CTkFrame(
            main_scroll,
            height=80,
            corner_radius=10,
            fg_color=("#2b2b2b", "#1a1a1a"),
            border_width=2,
            border_color=self.cyber_colors['neon_red']
        )
        title_frame.pack(fill="x", pady=(0, 20))
        title_frame.pack_propagate(False)
        
        title_label = ctk.CTkLabel(
            title_frame,
            text="◢◤ ADMIN CONTROL PANEL ◥◣",
            font=ctk.CTkFont(size=28, weight="bold", family="Consolas"),
            text_color=self.cyber_colors['neon_red']
        )
        title_label.pack(pady=20)
        
        # قسم إدارة المستخدمين
        users_frame = ctk.CTkFrame(
            main_scroll,
            corner_radius=10,
            fg_color=("#2b2b2b", "#1a1a1a"),
            border_width=1,
            border_color=self.cyber_colors['neon_blue']
        )
        users_frame.pack(fill="x", pady=(0, 20))
        
        users_title = ctk.CTkLabel(
            users_frame,
            text="┌─ USER MANAGEMENT ─┐",
            font=ctk.CTkFont(size=16, weight="bold", family="Consolas"),
            text_color=self.cyber_colors['neon_blue']
        )
        users_title.pack(pady=(15, 10))
        
        # أزرار إدارة المستخدمين
        user_buttons_frame = ctk.CTkFrame(users_frame, fg_color="transparent")
        user_buttons_frame.pack(fill="x", padx=20, pady=(0, 10))
        
        # زر عرض المستخدمين
        view_users_btn = ctk.CTkButton(
            user_buttons_frame,
            text="👥 VIEW USERS",
            width=150,
            height=40,
            font=ctk.CTkFont(size=12, family="Consolas"),
            fg_color=self.cyber_colors['neon_blue'],
            hover_color=self.cyber_colors['electric_blue'],
            text_color="white",
            command=self.show_users_list
        )
        view_users_btn.pack(side="left", padx=(0, 10))
        
        # زر إضافة مستخدم
        add_user_btn = ctk.CTkButton(
            user_buttons_frame,
            text="➕ ADD USER",
            width=150,
            height=40,
            font=ctk.CTkFont(size=12, family="Consolas"),
            fg_color=self.cyber_colors['neon_green'],
            hover_color="#00CC33",
            text_color="black",
            command=self.add_user_dialog
        )
        add_user_btn.pack(side="left", padx=5)
        
        # زر تعديل صلاحيات
        edit_perms_btn = ctk.CTkButton(
            user_buttons_frame,
            text="🔧 EDIT PERMISSIONS",
            width=180,
            height=40,
            font=ctk.CTkFont(size=12, family="Consolas"),
            fg_color=self.cyber_colors['neon_orange'],
            hover_color="#CC6600",
            text_color="white",
            command=self.edit_permissions_dialog
        )
        edit_perms_btn.pack(side="left", padx=5)
        
        # زر حذف مستخدم
        delete_user_btn = ctk.CTkButton(
            user_buttons_frame,
            text="🗑 DELETE USER",
            width=150,
            height=40,
            font=ctk.CTkFont(size=12, family="Consolas"),
            fg_color=self.cyber_colors['neon_red'],
            hover_color="#CC0000",
            text_color="white",
            command=self.delete_user_dialog
        )
        delete_user_btn.pack(side="right")
        
        # جدول المستخدمين
        self.users_table_frame = ctk.CTkFrame(
            users_frame,
            corner_radius=5,
            fg_color=("#3b3b3b", "#2a2a2a")
        )
        self.users_table_frame.pack(fill="x", padx=15, pady=(0, 15))
        
        # إنشاء جدول المستخدمين
        self.create_users_table()
        
        # قسم إحصائيات النظام
        stats_frame = ctk.CTkFrame(
            main_scroll,
            corner_radius=10,
            fg_color=("#2b2b2b", "#1a1a1a"),
            border_width=1,
            border_color=self.cyber_colors['neon_green']
        )
        stats_frame.pack(fill="x", pady=(0, 20))
        
        stats_title = ctk.CTkLabel(
            stats_frame,
            text="┌─ SYSTEM STATISTICS ─┐",
            font=ctk.CTkFont(size=16, weight="bold", family="Consolas"),
            text_color=self.cyber_colors['neon_green']
        )
        stats_title.pack(pady=(15, 10))
        
        # إحصائيات النظام
        stats_grid = ctk.CTkFrame(stats_frame, fg_color="transparent")
        stats_grid.pack(fill="x", padx=20, pady=(0, 20))
        
        # إجمالي المستخدمين
        self.total_users_card = self.create_stat_card(
            stats_grid, "TOTAL USERS", "0", self.cyber_colors['neon_blue']
        )
        self.total_users_card.pack(side="left", fill="both", expand=True, padx=(0, 10))
        
        # المستخدمين النشطين
        self.active_users_card = self.create_stat_card(
            stats_grid, "ACTIVE USERS", "0", self.cyber_colors['neon_green']
        )
        self.active_users_card.pack(side="left", fill="both", expand=True, padx=5)
        
        # عمليات الفحص
        self.scans_card = self.create_stat_card(
            stats_grid, "SECURITY SCANS", "0", self.cyber_colors['neon_purple']
        )
        self.scans_card.pack(side="left", fill="both", expand=True, padx=5)
        
        # التهديدات المكتشفة
        self.threats_card = self.create_stat_card(
            stats_grid, "THREATS DETECTED", "0", self.cyber_colors['neon_red']
        )
        self.threats_card.pack(side="left", fill="both", expand=True, padx=(10, 0))
        
        # قسم سجلات النظام
        logs_frame = ctk.CTkFrame(
            main_scroll,
            corner_radius=10,
            fg_color=("#2b2b2b", "#1a1a1a"),
            border_width=1,
            border_color=self.cyber_colors['neon_purple']
        )
        logs_frame.pack(fill="x", pady=(0, 20))
        
        logs_title = ctk.CTkLabel(
            logs_frame,
            text="┌─ SYSTEM LOGS ─┐",
            font=ctk.CTkFont(size=16, weight="bold", family="Consolas"),
            text_color=self.cyber_colors['neon_purple']
        )
        logs_title.pack(pady=(15, 10))
        
        # أزرار إدارة السجلات
        logs_buttons_frame = ctk.CTkFrame(logs_frame, fg_color="transparent")
        logs_buttons_frame.pack(fill="x", padx=20, pady=(0, 10))
        
        # زر عرض السجلات
        view_logs_btn = ctk.CTkButton(
            logs_buttons_frame,
            text="📋 VIEW LOGS",
            width=150,
            height=35,
            font=ctk.CTkFont(size=12, family="Consolas"),
            fg_color=self.cyber_colors['neon_purple'],
            hover_color="#9900CC",
            text_color="white",
            command=self.show_system_logs
        )
        view_logs_btn.pack(side="left", padx=(0, 10))
        
        # زر مسح السجلات
        clear_logs_btn = ctk.CTkButton(
            logs_buttons_frame,
            text="🗑 CLEAR LOGS",
            width=150,
            height=35,
            font=ctk.CTkFont(size=12, family="Consolas"),
            fg_color=self.cyber_colors['neon_red'],
            hover_color="#CC0000",
            text_color="white",
            command=self.clear_logs_dialog
        )
        clear_logs_btn.pack(side="left", padx=5)
        
        # زر تصدير السجلات
        export_logs_btn = ctk.CTkButton(
            logs_buttons_frame,
            text="📤 EXPORT LOGS",
            width=150,
            height=35,
            font=ctk.CTkFont(size=12, family="Consolas"),
            fg_color=self.cyber_colors['neon_orange'],
            hover_color="#CC6600",
            text_color="white",
            command=self.export_logs
        )
        export_logs_btn.pack(side="right")
        
        # منطقة عرض السجلات
        self.logs_display = ctk.CTkTextbox(
            logs_frame,
            width=800,
            height=200,
            font=ctk.CTkFont(size=9, family="Consolas"),
            fg_color=("#3b3b3b", "#2a2a2a"),
            text_color=self.cyber_colors['neon_green']
        )
        self.logs_display.pack(fill="both", expand=True, padx=15, pady=(0, 15))
        
        # قسم إعدادات النظام
        settings_frame = ctk.CTkFrame(
            main_scroll,
            corner_radius=10,
            fg_color=("#2b2b2b", "#1a1a1a"),
            border_width=1,
            border_color=self.cyber_colors['neon_orange']
        )
        settings_frame.pack(fill="both", expand=True)
        
        settings_title = ctk.CTkLabel(
            settings_frame,
            text="┌─ SYSTEM SETTINGS ─┐",
            font=ctk.CTkFont(size=16, weight="bold", family="Consolas"),
            text_color=self.cyber_colors['neon_orange']
        )
        settings_title.pack(pady=(15, 10))
        
        # أزرار الإعدادات
        settings_buttons_frame = ctk.CTkFrame(settings_frame, fg_color="transparent")
        settings_buttons_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        # زر نسخ احتياطي
        backup_btn = ctk.CTkButton(
            settings_buttons_frame,
            text="💾 BACKUP DATABASE",
            width=180,
            height=40,
            font=ctk.CTkFont(size=12, family="Consolas"),
            fg_color=self.cyber_colors['neon_blue'],
            hover_color=self.cyber_colors['electric_blue'],
            text_color="white",
            command=self.backup_database
        )
        backup_btn.pack(side="left", padx=(0, 10))
        
        # زر استعادة
        restore_btn = ctk.CTkButton(
            settings_buttons_frame,
            text="📥 RESTORE DATABASE",
            width=180,
            height=40,
            font=ctk.CTkFont(size=12, family="Consolas"),
            fg_color=self.cyber_colors['neon_green'],
            hover_color="#00CC33",
            text_color="black",
            command=self.restore_database
        )
        restore_btn.pack(side="left", padx=5)
        
        # زر إعادة تعيين النظام
        reset_btn = ctk.CTkButton(
            settings_buttons_frame,
            text="⚠ RESET SYSTEM",
            width=150,
            height=40,
            font=ctk.CTkFont(size=12, family="Consolas"),
            fg_color=self.cyber_colors['neon_red'],
            hover_color="#CC0000",
            text_color="white",
            command=self.reset_system_dialog
        )
        reset_btn.pack(side="right")
        
        # تحديث الإحصائيات
        self.update_admin_stats()
        
        # عرض آخر السجلات
        self.load_recent_logs()

    def create_stat_card(self, parent, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card = ctk.CTkFrame(
            parent,
            height=80,
            corner_radius=10,
            fg_color=("#2b2b2b", "#1a1a1a"),
            border_width=1,
            border_color=color
        )
        card.pack_propagate(False)

        # العنوان
        title_label = ctk.CTkLabel(
            card,
            text=title,
            font=ctk.CTkFont(size=9, family="Consolas"),
            text_color=color
        )
        title_label.pack(pady=(10, 2))

        # القيمة
        value_label = ctk.CTkLabel(
            card,
            text=value,
            font=ctk.CTkFont(size=16, weight="bold", family="Consolas"),
            text_color=color
        )
        value_label.pack(pady=(0, 10))

        # حفظ مرجع للتحديث
        card.value_label = value_label

        return card

    def create_users_table(self):
        """إنشاء جدول المستخدمين"""
        # عناوين الأعمدة
        headers = ["ID", "Username", "Email", "Role", "Status", "Last Login", "Actions"]
        header_frame = ctk.CTkFrame(self.users_table_frame, fg_color="transparent")
        header_frame.pack(fill="x", pady=5)

        for header in headers:
            header_label = ctk.CTkLabel(
                header_frame,
                text=header,
                font=ctk.CTkFont(size=10, weight="bold", family="Consolas"),
                text_color=self.cyber_colors['neon_blue'],
                width=100
            )
            header_label.pack(side="left", padx=5)

        # منطقة البيانات
        self.users_data_frame = ctk.CTkScrollableFrame(
            self.users_table_frame,
            height=150,
            fg_color=("#4b4b4b", "#3a3a3a")
        )
        self.users_data_frame.pack(fill="both", expand=True, padx=5, pady=5)

        # تحميل بيانات المستخدمين
        self.load_users_data()

    def load_users_data(self):
        """تحميل بيانات المستخدمين"""
        # مسح البيانات السابقة
        for widget in self.users_data_frame.winfo_children():
            widget.destroy()

        try:
            # جلب المستخدمين من قاعدة البيانات
            self.cursor.execute("""
                SELECT id, username, email, role, is_active, last_login
                FROM users ORDER BY id
            """)
            users = self.cursor.fetchall()

            for i, user in enumerate(users):
                self.add_user_row(user, i)

        except Exception as e:
            print(f"[ERROR] Failed to load users: {e}")

    def add_user_row(self, user, row_index):
        """إضافة صف مستخدم للجدول"""
        user_id, username, email, role, is_active, last_login = user

        row_frame = ctk.CTkFrame(
            self.users_data_frame,
            fg_color="transparent" if row_index % 2 == 0 else ("#5b5b5b", "#4a4a4a")
        )
        row_frame.pack(fill="x", pady=1)

        # ID
        id_label = ctk.CTkLabel(
            row_frame,
            text=str(user_id),
            font=ctk.CTkFont(size=9, family="Consolas"),
            text_color=self.cyber_colors['neon_green'],
            width=100
        )
        id_label.pack(side="left", padx=5)

        # اسم المستخدم
        username_label = ctk.CTkLabel(
            row_frame,
            text=username,
            font=ctk.CTkFont(size=9, family="Consolas"),
            text_color=self.cyber_colors['neon_blue'],
            width=100
        )
        username_label.pack(side="left", padx=5)

        # البريد الإلكتروني
        email_label = ctk.CTkLabel(
            row_frame,
            text=email[:20] + "..." if len(email) > 20 else email,
            font=ctk.CTkFont(size=9, family="Consolas"),
            text_color=self.cyber_colors['neon_purple'],
            width=100
        )
        email_label.pack(side="left", padx=5)

        # الدور
        role_color = self.cyber_colors['neon_red'] if role == 'admin' else self.cyber_colors['neon_green']
        role_label = ctk.CTkLabel(
            row_frame,
            text=role.upper(),
            font=ctk.CTkFont(size=9, family="Consolas"),
            text_color=role_color,
            width=100
        )
        role_label.pack(side="left", padx=5)

        # الحالة
        status_text = "ACTIVE" if is_active else "INACTIVE"
        status_color = self.cyber_colors['neon_green'] if is_active else self.cyber_colors['neon_red']
        status_label = ctk.CTkLabel(
            row_frame,
            text=status_text,
            font=ctk.CTkFont(size=9, family="Consolas"),
            text_color=status_color,
            width=100
        )
        status_label.pack(side="left", padx=5)

        # آخر تسجيل دخول
        last_login_text = last_login[:16] if last_login else "Never"
        login_label = ctk.CTkLabel(
            row_frame,
            text=last_login_text,
            font=ctk.CTkFont(size=9, family="Consolas"),
            text_color=self.cyber_colors['neon_orange'],
            width=100
        )
        login_label.pack(side="left", padx=5)

        # أزرار الإجراءات
        actions_frame = ctk.CTkFrame(row_frame, fg_color="transparent", width=100)
        actions_frame.pack(side="left", padx=5)
        actions_frame.pack_propagate(False)

        # زر تعديل
        edit_btn = ctk.CTkButton(
            actions_frame,
            text="✏",
            width=25,
            height=20,
            font=ctk.CTkFont(size=8),
            fg_color=self.cyber_colors['neon_blue'],
            command=lambda: self.edit_user_dialog(user_id)
        )
        edit_btn.pack(side="left", padx=1)

        # زر تفعيل/إلغاء تفعيل
        toggle_text = "❌" if is_active else "✅"
        toggle_btn = ctk.CTkButton(
            actions_frame,
            text=toggle_text,
            width=25,
            height=20,
            font=ctk.CTkFont(size=8),
            fg_color=self.cyber_colors['neon_orange'],
            command=lambda: self.toggle_user_status(user_id, is_active)
        )
        toggle_btn.pack(side="left", padx=1)

    def show_users_list(self):
        """عرض قائمة المستخدمين"""
        self.load_users_data()
        self.log_action("admin_view_users", "Viewed users list")

    def add_user_dialog(self):
        """حوار إضافة مستخدم جديد"""
        dialog = ctk.CTkToplevel(self.parent)
        dialog.title("◢◤ ADD NEW USER ◥◣")
        dialog.geometry("400x500")
        dialog.resizable(False, False)

        # العنوان
        title_label = ctk.CTkLabel(
            dialog,
            text="◢◤ CREATE NEW USER ◥◣",
            font=ctk.CTkFont(size=20, weight="bold", family="Consolas"),
            text_color=self.cyber_colors['neon_green']
        )
        title_label.pack(pady=(20, 30))

        # حقل اسم المستخدم
        username_label = ctk.CTkLabel(dialog, text="Username:", font=ctk.CTkFont(size=12, family="Consolas"))
        username_label.pack(pady=(0, 5))
        username_entry = ctk.CTkEntry(dialog, width=300, height=35)
        username_entry.pack(pady=(0, 15))

        # حقل البريد الإلكتروني
        email_label = ctk.CTkLabel(dialog, text="Email:", font=ctk.CTkFont(size=12, family="Consolas"))
        email_label.pack(pady=(0, 5))
        email_entry = ctk.CTkEntry(dialog, width=300, height=35)
        email_entry.pack(pady=(0, 15))

        # حقل كلمة المرور
        password_label = ctk.CTkLabel(dialog, text="Password:", font=ctk.CTkFont(size=12, family="Consolas"))
        password_label.pack(pady=(0, 5))
        password_entry = ctk.CTkEntry(dialog, width=300, height=35, show="*")
        password_entry.pack(pady=(0, 15))

        # اختيار الدور
        role_label = ctk.CTkLabel(dialog, text="Role:", font=ctk.CTkFont(size=12, family="Consolas"))
        role_label.pack(pady=(0, 5))
        role_var = ctk.StringVar(value="user")
        role_menu = ctk.CTkOptionMenu(dialog, values=["user", "admin"], variable=role_var, width=300)
        role_menu.pack(pady=(0, 20))

        # أزرار الإجراءات
        buttons_frame = ctk.CTkFrame(dialog, fg_color="transparent")
        buttons_frame.pack(pady=20)

        # زر الإنشاء
        create_btn = ctk.CTkButton(
            buttons_frame,
            text="CREATE USER",
            width=120,
            height=35,
            font=ctk.CTkFont(size=12, family="Consolas"),
            fg_color=self.cyber_colors['neon_green'],
            command=lambda: self.create_user(
                username_entry.get(), email_entry.get(),
                password_entry.get(), role_var.get(), dialog
            )
        )
        create_btn.pack(side="left", padx=(0, 10))

        # زر الإلغاء
        cancel_btn = ctk.CTkButton(
            buttons_frame,
            text="CANCEL",
            width=120,
            height=35,
            font=ctk.CTkFont(size=12, family="Consolas"),
            fg_color=self.cyber_colors['neon_red'],
            command=dialog.destroy
        )
        cancel_btn.pack(side="left")

    def create_user(self, username, email, password, role, dialog):
        """إنشاء مستخدم جديد"""
        # التحقق من صحة البيانات
        if not all([username, email, password]):
            messagebox.showerror("Error", "All fields are required!")
            return

        try:
            # تشفير كلمة المرور
            hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())

            # إنشاء صلاحيات افتراضية
            default_permissions = ["dashboard", "security_scan"] if role == "user" else [
                "admin_panel", "user_management", "system_control",
                "security_scan", "network_monitor", "view_logs",
                "system_settings", "backup_restore"
            ]

            # إدراج المستخدم الجديد
            self.cursor.execute("""
                INSERT INTO users (username, email, password, role, permissions)
                VALUES (?, ?, ?, ?, ?)
            """, (username, email, hashed_password, role, json.dumps(default_permissions)))

            self.conn.commit()

            # تسجيل العملية
            self.log_action("admin_create_user", f"Created user: {username} with role: {role}")

            # تحديث الجدول
            self.load_users_data()
            self.update_admin_stats()

            # إغلاق الحوار
            dialog.destroy()

            messagebox.showinfo("Success", f"User '{username}' created successfully!")

        except sqlite3.IntegrityError:
            messagebox.showerror("Error", "Username or email already exists!")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to create user: {str(e)}")

    def edit_user_dialog(self, user_id):
        """حوار تعديل المستخدم"""
        # جلب بيانات المستخدم
        self.cursor.execute("SELECT username, email, role FROM users WHERE id = ?", (user_id,))
        user_data = self.cursor.fetchone()

        if not user_data:
            messagebox.showerror("Error", "User not found!")
            return

        username, email, role = user_data

        dialog = ctk.CTkToplevel(self.parent)
        dialog.title(f"◢◤ EDIT USER: {username} ◥◣")
        dialog.geometry("400x400")
        dialog.resizable(False, False)

        # العنوان
        title_label = ctk.CTkLabel(
            dialog,
            text=f"◢◤ EDIT USER: {username} ◥◣",
            font=ctk.CTkFont(size=18, weight="bold", family="Consolas"),
            text_color=self.cyber_colors['neon_blue']
        )
        title_label.pack(pady=(20, 30))

        # حقل البريد الإلكتروني
        email_label = ctk.CTkLabel(dialog, text="Email:", font=ctk.CTkFont(size=12, family="Consolas"))
        email_label.pack(pady=(0, 5))
        email_entry = ctk.CTkEntry(dialog, width=300, height=35)
        email_entry.insert(0, email)
        email_entry.pack(pady=(0, 15))

        # اختيار الدور
        role_label = ctk.CTkLabel(dialog, text="Role:", font=ctk.CTkFont(size=12, family="Consolas"))
        role_label.pack(pady=(0, 5))
        role_var = ctk.StringVar(value=role)
        role_menu = ctk.CTkOptionMenu(dialog, values=["user", "admin"], variable=role_var, width=300)
        role_menu.pack(pady=(0, 20))

        # أزرار الإجراءات
        buttons_frame = ctk.CTkFrame(dialog, fg_color="transparent")
        buttons_frame.pack(pady=20)

        # زر التحديث
        update_btn = ctk.CTkButton(
            buttons_frame,
            text="UPDATE USER",
            width=120,
            height=35,
            font=ctk.CTkFont(size=12, family="Consolas"),
            fg_color=self.cyber_colors['neon_blue'],
            command=lambda: self.update_user(
                user_id, email_entry.get(), role_var.get(), dialog
            )
        )
        update_btn.pack(side="left", padx=(0, 10))

        # زر الإلغاء
        cancel_btn = ctk.CTkButton(
            buttons_frame,
            text="CANCEL",
            width=120,
            height=35,
            font=ctk.CTkFont(size=12, family="Consolas"),
            fg_color=self.cyber_colors['neon_red'],
            command=dialog.destroy
        )
        cancel_btn.pack(side="left")

    def update_user(self, user_id, email, role, dialog):
        """تحديث بيانات المستخدم"""
        try:
            self.cursor.execute("""
                UPDATE users SET email = ?, role = ? WHERE id = ?
            """, (email, role, user_id))

            self.conn.commit()

            # تسجيل العملية
            self.log_action("admin_update_user", f"Updated user ID: {user_id}")

            # تحديث الجدول
            self.load_users_data()

            # إغلاق الحوار
            dialog.destroy()

            messagebox.showinfo("Success", "User updated successfully!")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to update user: {str(e)}")

    def toggle_user_status(self, user_id, current_status):
        """تفعيل/إلغاء تفعيل المستخدم"""
        new_status = not current_status

        try:
            self.cursor.execute("UPDATE users SET is_active = ? WHERE id = ?", (new_status, user_id))
            self.conn.commit()

            status_text = "activated" if new_status else "deactivated"
            self.log_action("admin_toggle_user", f"User ID {user_id} {status_text}")

            self.load_users_data()
            self.update_admin_stats()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to update user status: {str(e)}")

    def delete_user_dialog(self):
        """حوار حذف المستخدم"""
        username = simpledialog.askstring("Delete User", "Enter username to delete:")
        if not username:
            return

        # التأكيد
        confirm = messagebox.askyesno(
            "Confirm Deletion",
            f"Are you sure you want to delete user '{username}'?\nThis action cannot be undone!"
        )

        if confirm:
            try:
                self.cursor.execute("DELETE FROM users WHERE username = ?", (username,))
                if self.cursor.rowcount > 0:
                    self.conn.commit()
                    self.log_action("admin_delete_user", f"Deleted user: {username}")
                    self.load_users_data()
                    self.update_admin_stats()
                    messagebox.showinfo("Success", f"User '{username}' deleted successfully!")
                else:
                    messagebox.showerror("Error", f"User '{username}' not found!")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to delete user: {str(e)}")

    def edit_permissions_dialog(self):
        """حوار تعديل الصلاحيات"""
        username = simpledialog.askstring("Edit Permissions", "Enter username:")
        if not username:
            return

        # جلب المستخدم
        self.cursor.execute("SELECT id, permissions FROM users WHERE username = ?", (username,))
        user_data = self.cursor.fetchone()

        if not user_data:
            messagebox.showerror("Error", f"User '{username}' not found!")
            return

        user_id, permissions_json = user_data
        current_permissions = json.loads(permissions_json) if permissions_json else []

        # إنشاء نافذة الصلاحيات
        dialog = ctk.CTkToplevel(self.parent)
        dialog.title(f"◢◤ PERMISSIONS: {username} ◥◣")
        dialog.geometry("500x600")
        dialog.resizable(False, False)

        # العنوان
        title_label = ctk.CTkLabel(
            dialog,
            text=f"◢◤ EDIT PERMISSIONS: {username} ◥◣",
            font=ctk.CTkFont(size=18, weight="bold", family="Consolas"),
            text_color=self.cyber_colors['neon_orange']
        )
        title_label.pack(pady=(20, 30))

        # قائمة الصلاحيات
        permissions_frame = ctk.CTkScrollableFrame(dialog, width=450, height=400)
        permissions_frame.pack(padx=25, pady=(0, 20))

        available_permissions = [
            "dashboard", "security_scan", "network_monitor", "learning_center",
            "system_reports", "settings", "admin_panel", "user_management",
            "system_control", "view_logs", "system_settings", "backup_restore"
        ]

        permission_vars = {}
        for perm in available_permissions:
            var = ctk.BooleanVar(value=perm in current_permissions)
            permission_vars[perm] = var

            checkbox = ctk.CTkCheckBox(
                permissions_frame,
                text=perm.replace("_", " ").title(),
                variable=var,
                font=ctk.CTkFont(size=12, family="Consolas")
            )
            checkbox.pack(anchor="w", pady=5, padx=20)

        # أزرار الإجراءات
        buttons_frame = ctk.CTkFrame(dialog, fg_color="transparent")
        buttons_frame.pack(pady=20)

        # زر الحفظ
        save_btn = ctk.CTkButton(
            buttons_frame,
            text="SAVE PERMISSIONS",
            width=150,
            height=35,
            font=ctk.CTkFont(size=12, family="Consolas"),
            fg_color=self.cyber_colors['neon_green'],
            command=lambda: self.save_permissions(user_id, permission_vars, dialog)
        )
        save_btn.pack(side="left", padx=(0, 10))

        # زر الإلغاء
        cancel_btn = ctk.CTkButton(
            buttons_frame,
            text="CANCEL",
            width=120,
            height=35,
            font=ctk.CTkFont(size=12, family="Consolas"),
            fg_color=self.cyber_colors['neon_red'],
            command=dialog.destroy
        )
        cancel_btn.pack(side="left")

    def save_permissions(self, user_id, permission_vars, dialog):
        """حفظ الصلاحيات"""
        try:
            # جمع الصلاحيات المحددة
            selected_permissions = [
                perm for perm, var in permission_vars.items() if var.get()
            ]

            # تحديث قاعدة البيانات
            self.cursor.execute(
                "UPDATE users SET permissions = ? WHERE id = ?",
                (json.dumps(selected_permissions), user_id)
            )
            self.conn.commit()

            # تسجيل العملية
            self.log_action("admin_update_permissions", f"Updated permissions for user ID: {user_id}")

            # إغلاق الحوار
            dialog.destroy()

            messagebox.showinfo("Success", "Permissions updated successfully!")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to update permissions: {str(e)}")

    def update_admin_stats(self):
        """تحديث إحصائيات الأدمن"""
        try:
            # إجمالي المستخدمين
            self.cursor.execute("SELECT COUNT(*) FROM users")
            total_users = self.cursor.fetchone()[0]
            self.total_users_card.value_label.configure(text=str(total_users))

            # المستخدمين النشطين
            self.cursor.execute("SELECT COUNT(*) FROM users WHERE is_active = 1")
            active_users = self.cursor.fetchone()[0]
            self.active_users_card.value_label.configure(text=str(active_users))

            # عمليات الفحص
            self.cursor.execute("SELECT COUNT(*) FROM security_scans")
            scans_count = self.cursor.fetchone()[0]
            self.scans_card.value_label.configure(text=str(scans_count))

            # التهديدات المكتشفة
            self.cursor.execute("SELECT SUM(threats_found) FROM security_scans")
            threats_result = self.cursor.fetchone()[0]
            threats_count = threats_result if threats_result else 0
            self.threats_card.value_label.configure(text=str(threats_count))

        except Exception as e:
            print(f"[ERROR] Failed to update admin stats: {e}")

    def show_system_logs(self):
        """عرض سجلات النظام"""
        self.load_recent_logs()
        self.log_action("admin_view_logs", "Viewed system logs")

    def load_recent_logs(self):
        """تحميل آخر السجلات"""
        try:
            self.cursor.execute("""
                SELECT sl.action, sl.details, sl.timestamp, u.username
                FROM system_logs sl
                LEFT JOIN users u ON sl.user_id = u.id
                ORDER BY sl.timestamp DESC
                LIMIT 50
            """)
            logs = self.cursor.fetchall()

            # مسح المحتوى السابق
            self.logs_display.delete("1.0", "end")

            # إضافة السجلات
            self.logs_display.insert("1.0", "◢◤ SYSTEM LOGS ◥◣\n")
            self.logs_display.insert("end", "═" * 80 + "\n\n")

            for action, details, timestamp, username in logs:
                user_text = username if username else "SYSTEM"
                log_entry = f"[{timestamp}] {user_text}: {action}\n"
                if details:
                    log_entry += f"    Details: {details}\n"
                log_entry += "\n"
                self.logs_display.insert("end", log_entry)

        except Exception as e:
            self.logs_display.delete("1.0", "end")
            self.logs_display.insert("1.0", f"Error loading logs: {str(e)}")

    def clear_logs_dialog(self):
        """حوار مسح السجلات"""
        confirm = messagebox.askyesno(
            "Clear Logs",
            "Are you sure you want to clear all system logs?\nThis action cannot be undone!"
        )

        if confirm:
            try:
                self.cursor.execute("DELETE FROM system_logs")
                self.conn.commit()

                self.log_action("admin_clear_logs", "Cleared all system logs")
                self.load_recent_logs()

                messagebox.showinfo("Success", "System logs cleared successfully!")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to clear logs: {str(e)}")

    def export_logs(self):
        """تصدير السجلات"""
        try:
            from tkinter import filedialog
            filename = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
                title="Export System Logs"
            )

            if filename:
                # جلب جميع السجلات
                self.cursor.execute("""
                    SELECT sl.action, sl.details, sl.timestamp, u.username
                    FROM system_logs sl
                    LEFT JOIN users u ON sl.user_id = u.id
                    ORDER BY sl.timestamp DESC
                """)
                logs = self.cursor.fetchall()

                # كتابة السجلات للملف
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write("◢◤ CYBER SECURITY CENTER - SYSTEM LOGS ◥◣\n")
                    f.write("═" * 80 + "\n")
                    f.write(f"Export Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write("═" * 80 + "\n\n")

                    for action, details, timestamp, username in logs:
                        user_text = username if username else "SYSTEM"
                        f.write(f"[{timestamp}] {user_text}: {action}\n")
                        if details:
                            f.write(f"    Details: {details}\n")
                        f.write("\n")

                self.log_action("admin_export_logs", f"Exported logs to {filename}")
                messagebox.showinfo("Success", f"Logs exported successfully to:\n{filename}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to export logs: {str(e)}")

    def backup_database(self):
        """نسخ احتياطي لقاعدة البيانات"""
        try:
            from tkinter import filedialog
            filename = filedialog.asksaveasfilename(
                defaultextension=".db",
                filetypes=[("Database files", "*.db"), ("All files", "*.*")],
                title="Backup Database"
            )

            if filename:
                import shutil
                shutil.copy2("cyber_security.db", filename)

                self.log_action("admin_backup_db", f"Database backed up to {filename}")
                messagebox.showinfo("Success", f"Database backed up successfully to:\n{filename}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to backup database: {str(e)}")

    def restore_database(self):
        """استعادة قاعدة البيانات"""
        try:
            from tkinter import filedialog
            filename = filedialog.askopenfilename(
                filetypes=[("Database files", "*.db"), ("All files", "*.*")],
                title="Restore Database"
            )

            if filename:
                confirm = messagebox.askyesno(
                    "Confirm Restore",
                    "Are you sure you want to restore the database?\nThis will overwrite all current data!"
                )

                if confirm:
                    import shutil
                    shutil.copy2(filename, "cyber_security.db")

                    self.log_action("admin_restore_db", f"Database restored from {filename}")
                    messagebox.showinfo("Success", "Database restored successfully!\nPlease restart the application.")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to restore database: {str(e)}")

    def reset_system_dialog(self):
        """حوار إعادة تعيين النظام"""
        confirm = messagebox.askyesno(
            "Reset System",
            "⚠️ WARNING ⚠️\n\nThis will reset the entire system including:\n• All user accounts (except admin)\n• All logs\n• All scan results\n\nThis action cannot be undone!\n\nAre you sure you want to continue?"
        )

        if confirm:
            # تأكيد إضافي
            final_confirm = messagebox.askyesno(
                "Final Confirmation",
                "This is your final warning!\n\nType 'RESET' to confirm system reset."
            )

            if final_confirm:
                reset_code = simpledialog.askstring("Security Check", "Enter 'RESET' to confirm:")

                if reset_code == "RESET":
                    try:
                        # حذف جميع البيانات عدا الأدمن
                        self.cursor.execute("DELETE FROM users WHERE role != 'admin'")
                        self.cursor.execute("DELETE FROM system_logs")
                        self.cursor.execute("DELETE FROM security_scans")
                        self.cursor.execute("DELETE FROM network_logs")

                        self.conn.commit()

                        self.log_action("admin_system_reset", "System reset performed")

                        # تحديث الواجهة
                        self.load_users_data()
                        self.update_admin_stats()
                        self.load_recent_logs()

                        messagebox.showinfo("Success", "System reset completed successfully!")

                    except Exception as e:
                        messagebox.showerror("Error", f"Failed to reset system: {str(e)}")
                else:
                    messagebox.showinfo("Cancelled", "System reset cancelled.")
