"""
وحدة مراقب الشبكة
Network Monitor Module for Cyber Security Center
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import ttk
import threading
import time
import socket
from datetime import datetime
import json

# محاولة استيراد المكتبات الاختيارية
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    print("[WARNING] psutil not available, using mock network data")

class NetworkMonitorModule:
    def __init__(self, parent, cyber_colors, log_action, cursor, conn):
        self.parent = parent
        self.cyber_colors = cyber_colors
        self.log_action = log_action
        self.cursor = cursor
        self.conn = conn
        self.monitoring_active = False
        self.network_data = []
        
    def create_network_monitor(self):
        """إنشاء واجهة مراقب الشبكة"""
        # مسح المحتوى السابق
        for widget in self.parent.winfo_children():
            widget.destroy()
        
        # إطار التمرير الرئيسي
        main_scroll = ctk.CTkScrollableFrame(
            self.parent,
            corner_radius=0,
            fg_color=("#1a1a1a", "#0d1117")
        )
        main_scroll.pack(fill="both", expand=True, padx=10, pady=10)
        
        # العنوان الرئيسي
        title_frame = ctk.CTkFrame(
            main_scroll,
            height=80,
            corner_radius=10,
            fg_color=("#2b2b2b", "#1a1a1a"),
            border_width=2,
            border_color=self.cyber_colors['neon_blue']
        )
        title_frame.pack(fill="x", pady=(0, 20))
        title_frame.pack_propagate(False)
        
        title_label = ctk.CTkLabel(
            title_frame,
            text="◢◤ NETWORK MONITOR ◥◣",
            font=ctk.CTkFont(size=28, weight="bold", family="Consolas"),
            text_color=self.cyber_colors['neon_blue']
        )
        title_label.pack(pady=20)
        
        # قسم التحكم
        control_frame = ctk.CTkFrame(
            main_scroll,
            corner_radius=10,
            fg_color=("#2b2b2b", "#1a1a1a"),
            border_width=1,
            border_color=self.cyber_colors['neon_green']
        )
        control_frame.pack(fill="x", pady=(0, 20))
        
        control_title = ctk.CTkLabel(
            control_frame,
            text="┌─ MONITORING CONTROLS ─┐",
            font=ctk.CTkFont(size=16, weight="bold", family="Consolas"),
            text_color=self.cyber_colors['neon_green']
        )
        control_title.pack(pady=(15, 10))
        
        # أزرار التحكم
        buttons_frame = ctk.CTkFrame(control_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        # زر بدء المراقبة
        self.start_btn = ctk.CTkButton(
            buttons_frame,
            text="▶ START MONITORING",
            width=200,
            height=40,
            font=ctk.CTkFont(size=12, family="Consolas"),
            fg_color=self.cyber_colors['neon_green'],
            hover_color="#00CC33",
            text_color="black",
            command=self.start_monitoring
        )
        self.start_btn.pack(side="left", padx=(0, 10))
        
        # زر إيقاف المراقبة
        self.stop_btn = ctk.CTkButton(
            buttons_frame,
            text="⏹ STOP MONITORING",
            width=200,
            height=40,
            font=ctk.CTkFont(size=12, family="Consolas"),
            fg_color=self.cyber_colors['neon_red'],
            hover_color="#CC0000",
            text_color="white",
            command=self.stop_monitoring,
            state="disabled"
        )
        self.stop_btn.pack(side="left", padx=5)
        
        # زر مسح البيانات
        clear_btn = ctk.CTkButton(
            buttons_frame,
            text="🗑 CLEAR DATA",
            width=150,
            height=40,
            font=ctk.CTkFont(size=12, family="Consolas"),
            fg_color=self.cyber_colors['neon_purple'],
            hover_color="#9900CC",
            text_color="white",
            command=self.clear_data
        )
        clear_btn.pack(side="left", padx=5)
        
        # زر تصدير البيانات
        export_btn = ctk.CTkButton(
            buttons_frame,
            text="📤 EXPORT DATA",
            width=150,
            height=40,
            font=ctk.CTkFont(size=12, family="Consolas"),
            fg_color=self.cyber_colors['neon_orange'],
            hover_color="#CC6600",
            text_color="white",
            command=self.export_data
        )
        export_btn.pack(side="right")
        
        # قسم الإحصائيات
        stats_frame = ctk.CTkFrame(main_scroll, fg_color="transparent")
        stats_frame.pack(fill="x", pady=(0, 20))
        
        # إحصائيات الاتصالات
        self.connections_card = self.create_stat_card(
            stats_frame, "ACTIVE CONNECTIONS", "0", self.cyber_colors['neon_blue']
        )
        self.connections_card.pack(side="left", fill="both", expand=True, padx=(0, 10))
        
        # إحصائيات البيانات المرسلة
        self.sent_card = self.create_stat_card(
            stats_frame, "DATA SENT", "0 MB", self.cyber_colors['neon_green']
        )
        self.sent_card.pack(side="left", fill="both", expand=True, padx=5)
        
        # إحصائيات البيانات المستقبلة
        self.received_card = self.create_stat_card(
            stats_frame, "DATA RECEIVED", "0 MB", self.cyber_colors['neon_purple']
        )
        self.received_card.pack(side="left", fill="both", expand=True, padx=5)
        
        # إحصائيات التهديدات
        self.threats_card = self.create_stat_card(
            stats_frame, "THREATS DETECTED", "0", self.cyber_colors['neon_red']
        )
        self.threats_card.pack(side="left", fill="both", expand=True, padx=(10, 0))
        
        # قسم حالة المراقبة
        self.status_frame = ctk.CTkFrame(
            main_scroll,
            corner_radius=10,
            fg_color=("#2b2b2b", "#1a1a1a"),
            border_width=1,
            border_color=self.cyber_colors['neon_orange']
        )
        self.status_frame.pack(fill="x", pady=(0, 20))
        
        status_title = ctk.CTkLabel(
            self.status_frame,
            text="┌─ MONITORING STATUS ─┐",
            font=ctk.CTkFont(size=16, weight="bold", family="Consolas"),
            text_color=self.cyber_colors['neon_orange']
        )
        status_title.pack(pady=(15, 10))
        
        # رسالة الحالة
        self.status_label = ctk.CTkLabel(
            self.status_frame,
            text="[STATUS] Network monitoring is offline",
            font=ctk.CTkFont(size=12, family="Consolas"),
            text_color=self.cyber_colors['neon_red']
        )
        self.status_label.pack(pady=(0, 15))
        
        # قسم جدول الاتصالات
        connections_frame = ctk.CTkFrame(
            main_scroll,
            corner_radius=10,
            fg_color=("#2b2b2b", "#1a1a1a"),
            border_width=1,
            border_color=self.cyber_colors['neon_purple']
        )
        connections_frame.pack(fill="both", expand=True)
        
        connections_title = ctk.CTkLabel(
            connections_frame,
            text="┌─ NETWORK CONNECTIONS ─┐",
            font=ctk.CTkFont(size=16, weight="bold", family="Consolas"),
            text_color=self.cyber_colors['neon_purple']
        )
        connections_title.pack(pady=(15, 10))
        
        # جدول الاتصالات
        self.create_connections_table(connections_frame)
        
        # بدء تحديث الإحصائيات
        self.update_network_stats()
    
    def create_stat_card(self, parent, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card = ctk.CTkFrame(
            parent,
            height=100,
            corner_radius=10,
            fg_color=("#2b2b2b", "#1a1a1a"),
            border_width=2,
            border_color=color
        )
        card.pack_propagate(False)
        
        # العنوان
        title_label = ctk.CTkLabel(
            card,
            text=title,
            font=ctk.CTkFont(size=10, family="Consolas"),
            text_color=color
        )
        title_label.pack(pady=(10, 2))
        
        # القيمة
        value_label = ctk.CTkLabel(
            card,
            text=value,
            font=ctk.CTkFont(size=18, weight="bold", family="Consolas"),
            text_color=color
        )
        value_label.pack(pady=(0, 10))
        
        # حفظ مرجع للتحديث
        card.value_label = value_label
        
        return card
    
    def create_connections_table(self, parent):
        """إنشاء جدول الاتصالات"""
        # إطار الجدول
        table_frame = ctk.CTkFrame(
            parent,
            fg_color=("#3b3b3b", "#2a2a2a")
        )
        table_frame.pack(fill="both", expand=True, padx=15, pady=(0, 15))
        
        # عناوين الأعمدة
        headers = ["Protocol", "Local Address", "Remote Address", "Status", "Process"]
        header_frame = ctk.CTkFrame(table_frame, fg_color="transparent")
        header_frame.pack(fill="x", pady=5)
        
        for i, header in enumerate(headers):
            header_label = ctk.CTkLabel(
                header_frame,
                text=header,
                font=ctk.CTkFont(size=10, weight="bold", family="Consolas"),
                text_color=self.cyber_colors['neon_blue'],
                width=120
            )
            header_label.pack(side="left", padx=5)
        
        # منطقة البيانات
        self.data_frame = ctk.CTkScrollableFrame(
            table_frame,
            height=200,
            fg_color=("#4b4b4b", "#3a3a3a")
        )
        self.data_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # إضافة بيانات تجريبية
        self.populate_connections_table()
    
    def populate_connections_table(self):
        """ملء جدول الاتصالات بالبيانات"""
        # مسح البيانات السابقة
        for widget in self.data_frame.winfo_children():
            widget.destroy()
        
        if PSUTIL_AVAILABLE:
            try:
                connections = psutil.net_connections(kind='inet')
                for i, conn in enumerate(connections[:20]):  # عرض أول 20 اتصال
                    self.add_connection_row(conn, i)
            except Exception as e:
                print(f"[ERROR] Failed to get connections: {e}")
                self.add_mock_connections()
        else:
            self.add_mock_connections()
    
    def add_connection_row(self, conn, row_index):
        """إضافة صف اتصال للجدول"""
        row_frame = ctk.CTkFrame(
            self.data_frame,
            fg_color="transparent" if row_index % 2 == 0 else ("#5b5b5b", "#4a4a4a")
        )
        row_frame.pack(fill="x", pady=1)
        
        # البروتوكول
        protocol = "TCP" if conn.type == socket.SOCK_STREAM else "UDP"
        protocol_label = ctk.CTkLabel(
            row_frame,
            text=protocol,
            font=ctk.CTkFont(size=9, family="Consolas"),
            text_color=self.cyber_colors['neon_green'],
            width=120
        )
        protocol_label.pack(side="left", padx=5)
        
        # العنوان المحلي
        local_addr = f"{conn.laddr.ip}:{conn.laddr.port}" if conn.laddr else "N/A"
        local_label = ctk.CTkLabel(
            row_frame,
            text=local_addr,
            font=ctk.CTkFont(size=9, family="Consolas"),
            text_color=self.cyber_colors['neon_blue'],
            width=120
        )
        local_label.pack(side="left", padx=5)
        
        # العنوان البعيد
        remote_addr = f"{conn.raddr.ip}:{conn.raddr.port}" if conn.raddr else "N/A"
        remote_label = ctk.CTkLabel(
            row_frame,
            text=remote_addr,
            font=ctk.CTkFont(size=9, family="Consolas"),
            text_color=self.cyber_colors['neon_purple'],
            width=120
        )
        remote_label.pack(side="left", padx=5)
        
        # الحالة
        status_color = self.cyber_colors['neon_green'] if conn.status == 'ESTABLISHED' else self.cyber_colors['neon_orange']
        status_label = ctk.CTkLabel(
            row_frame,
            text=conn.status,
            font=ctk.CTkFont(size=9, family="Consolas"),
            text_color=status_color,
            width=120
        )
        status_label.pack(side="left", padx=5)
        
        # العملية
        try:
            process_name = psutil.Process(conn.pid).name() if conn.pid else "N/A"
        except:
            process_name = "N/A"
        
        process_label = ctk.CTkLabel(
            row_frame,
            text=process_name,
            font=ctk.CTkFont(size=9, family="Consolas"),
            text_color=self.cyber_colors['matrix_green'],
            width=120
        )
        process_label.pack(side="left", padx=5)
    
    def add_mock_connections(self):
        """إضافة اتصالات وهمية للعرض"""
        mock_connections = [
            ("TCP", "192.168.1.100:443", "74.125.224.72:443", "ESTABLISHED", "chrome.exe"),
            ("TCP", "192.168.1.100:80", "151.101.193.140:80", "ESTABLISHED", "firefox.exe"),
            ("UDP", "192.168.1.100:53", "8.8.8.8:53", "NONE", "dns.exe"),
            ("TCP", "127.0.0.1:3306", "N/A", "LISTEN", "mysqld.exe"),
            ("TCP", "0.0.0.0:22", "N/A", "LISTEN", "sshd.exe")
        ]
        
        for i, (protocol, local, remote, status, process) in enumerate(mock_connections):
            row_frame = ctk.CTkFrame(
                self.data_frame,
                fg_color="transparent" if i % 2 == 0 else ("#5b5b5b", "#4a4a4a")
            )
            row_frame.pack(fill="x", pady=1)
            
            # إضافة البيانات
            for j, (text, color) in enumerate([
                (protocol, self.cyber_colors['neon_green']),
                (local, self.cyber_colors['neon_blue']),
                (remote, self.cyber_colors['neon_purple']),
                (status, self.cyber_colors['neon_green'] if status == 'ESTABLISHED' else self.cyber_colors['neon_orange']),
                (process, self.cyber_colors['matrix_green'])
            ]):
                label = ctk.CTkLabel(
                    row_frame,
                    text=text,
                    font=ctk.CTkFont(size=9, family="Consolas"),
                    text_color=color,
                    width=120
                )
                label.pack(side="left", padx=5)

    def start_monitoring(self):
        """بدء مراقبة الشبكة"""
        if self.monitoring_active:
            return

        self.monitoring_active = True
        self.start_btn.configure(state="disabled")
        self.stop_btn.configure(state="normal")
        self.status_label.configure(
            text="[STATUS] Network monitoring is ACTIVE",
            text_color=self.cyber_colors['neon_green']
        )

        # بدء مراقبة الشبكة في thread منفصل
        monitor_thread = threading.Thread(target=self.monitor_network, daemon=True)
        monitor_thread.start()

        # تسجيل العملية
        self.log_action("network_monitor_start", "Network monitoring started")

    def stop_monitoring(self):
        """إيقاف مراقبة الشبكة"""
        self.monitoring_active = False
        self.start_btn.configure(state="normal")
        self.stop_btn.configure(state="disabled")
        self.status_label.configure(
            text="[STATUS] Network monitoring is OFFLINE",
            text_color=self.cyber_colors['neon_red']
        )

        # تسجيل العملية
        self.log_action("network_monitor_stop", "Network monitoring stopped")

    def clear_data(self):
        """مسح بيانات المراقبة"""
        self.network_data.clear()
        self.populate_connections_table()

        # إعادة تعيين الإحصائيات
        self.connections_card.value_label.configure(text="0")
        self.sent_card.value_label.configure(text="0 MB")
        self.received_card.value_label.configure(text="0 MB")
        self.threats_card.value_label.configure(text="0")

        # تسجيل العملية
        self.log_action("network_data_clear", "Network monitoring data cleared")

    def export_data(self):
        """تصدير بيانات المراقبة"""
        try:
            from tkinter import filedialog
            filename = filedialog.asksaveasfilename(
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
                title="Export Network Data"
            )

            if filename:
                export_data = {
                    "timestamp": datetime.now().isoformat(),
                    "connections": self.network_data,
                    "stats": {
                        "active_connections": self.connections_card.value_label.cget("text"),
                        "data_sent": self.sent_card.value_label.cget("text"),
                        "data_received": self.received_card.value_label.cget("text"),
                        "threats_detected": self.threats_card.value_label.cget("text")
                    }
                }

                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, indent=2, ensure_ascii=False)

                # تسجيل العملية
                self.log_action("network_data_export", f"Network data exported to {filename}")

                # عرض رسالة نجاح
                from tkinter import messagebox
                messagebox.showinfo("✓ EXPORT SUCCESS", f"Network data exported successfully to:\n{filename}")

        except Exception as e:
            from tkinter import messagebox
            messagebox.showerror("⚠ EXPORT ERROR", f"Failed to export data:\n{str(e)}")

    def monitor_network(self):
        """مراقبة الشبكة في الخلفية"""
        while self.monitoring_active:
            try:
                # جمع بيانات الشبكة
                self.collect_network_data()

                # تحديث الجدول
                self.populate_connections_table()

                # انتظار قبل التحديث التالي
                time.sleep(5)  # تحديث كل 5 ثواني

            except Exception as e:
                print(f"[ERROR] Network monitoring error: {e}")
                time.sleep(10)

    def collect_network_data(self):
        """جمع بيانات الشبكة"""
        try:
            if PSUTIL_AVAILABLE:
                # جمع إحصائيات الشبكة
                net_io = psutil.net_io_counters()
                connections = psutil.net_connections(kind='inet')

                # حفظ البيانات
                network_entry = {
                    "timestamp": datetime.now().isoformat(),
                    "bytes_sent": net_io.bytes_sent,
                    "bytes_recv": net_io.bytes_recv,
                    "packets_sent": net_io.packets_sent,
                    "packets_recv": net_io.packets_recv,
                    "connections_count": len(connections)
                }

                self.network_data.append(network_entry)

                # الاحتفاظ بآخر 100 إدخال فقط
                if len(self.network_data) > 100:
                    self.network_data = self.network_data[-100:]

                # حفظ في قاعدة البيانات
                self.save_network_data(network_entry)

            else:
                # بيانات وهمية
                import random
                network_entry = {
                    "timestamp": datetime.now().isoformat(),
                    "bytes_sent": random.randint(1000000, 10000000),
                    "bytes_recv": random.randint(1000000, 10000000),
                    "packets_sent": random.randint(1000, 10000),
                    "packets_recv": random.randint(1000, 10000),
                    "connections_count": random.randint(10, 50)
                }

                self.network_data.append(network_entry)

        except Exception as e:
            print(f"[ERROR] Failed to collect network data: {e}")

    def save_network_data(self, data):
        """حفظ بيانات الشبكة في قاعدة البيانات"""
        try:
            self.cursor.execute('''
                INSERT INTO network_logs (connection_type, local_address, remote_address, status, process_name)
                VALUES (?, ?, ?, ?, ?)
            ''', ("monitoring", "system", "various", "active", "network_monitor"))
            self.conn.commit()
        except Exception as e:
            print(f"[ERROR] Failed to save network data: {e}")

    def update_network_stats(self):
        """تحديث إحصائيات الشبكة"""
        def update():
            while True:
                try:
                    if PSUTIL_AVAILABLE:
                        # تحديث الإحصائيات الحقيقية
                        net_io = psutil.net_io_counters()
                        connections = psutil.net_connections(kind='inet')

                        # تحديث عدد الاتصالات
                        self.connections_card.value_label.configure(text=str(len(connections)))

                        # تحديث البيانات المرسلة (بالميجابايت)
                        sent_mb = net_io.bytes_sent / (1024 * 1024)
                        self.sent_card.value_label.configure(text=f"{sent_mb:.1f} MB")

                        # تحديث البيانات المستقبلة (بالميجابايت)
                        recv_mb = net_io.bytes_recv / (1024 * 1024)
                        self.received_card.value_label.configure(text=f"{recv_mb:.1f} MB")

                        # تحديث التهديدات (محاكاة)
                        threats = self.detect_threats(connections)
                        self.threats_card.value_label.configure(text=str(len(threats)))

                    else:
                        # بيانات وهمية
                        import random
                        self.connections_card.value_label.configure(text=str(random.randint(10, 50)))
                        self.sent_card.value_label.configure(text=f"{random.randint(100, 1000):.1f} MB")
                        self.received_card.value_label.configure(text=f"{random.randint(200, 2000):.1f} MB")
                        self.threats_card.value_label.configure(text=str(random.randint(0, 5)))

                    time.sleep(3)  # تحديث كل 3 ثواني

                except Exception as e:
                    print(f"[ERROR] Stats update error: {e}")
                    time.sleep(10)

        # تشغيل التحديث في thread منفصل
        stats_thread = threading.Thread(target=update, daemon=True)
        stats_thread.start()

    def detect_threats(self, connections):
        """كشف التهديدات في الاتصالات"""
        threats = []

        # قائمة بالمنافذ المشبوهة
        suspicious_ports = [1337, 31337, 12345, 54321, 9999]

        # قائمة بالعناوين المشبوهة (محاكاة)
        suspicious_ips = ["192.168.1.666", "10.0.0.666", "172.16.0.666"]

        for conn in connections:
            # فحص المنافذ المشبوهة
            if conn.laddr and conn.laddr.port in suspicious_ports:
                threats.append({
                    "type": "suspicious_port",
                    "details": f"Suspicious port {conn.laddr.port} detected",
                    "connection": conn
                })

            # فحص العناوين المشبوهة
            if conn.raddr and conn.raddr.ip in suspicious_ips:
                threats.append({
                    "type": "suspicious_ip",
                    "details": f"Connection to suspicious IP {conn.raddr.ip}",
                    "connection": conn
                })

        return threats
