"""
وحدة مركز التعليم
Learning Center Module for Cyber Security Center
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import ttk
import threading
import time
from datetime import datetime
import json

class LearningCenterModule:
    def __init__(self, parent, cyber_colors, log_action, cursor, conn):
        self.parent = parent
        self.cyber_colors = cyber_colors
        self.log_action = log_action
        self.cursor = cursor
        self.conn = conn
        self.current_lesson = None
        self.user_progress = {}
        
    def create_learning_center(self):
        """إنشاء واجهة مركز التعليم"""
        # مسح المحتوى السابق
        for widget in self.parent.winfo_children():
            widget.destroy()
        
        # إطار التمرير الرئيسي
        main_scroll = ctk.CTkScrollableFrame(
            self.parent,
            corner_radius=0,
            fg_color=("#1a1a1a", "#0d1117")
        )
        main_scroll.pack(fill="both", expand=True, padx=10, pady=10)
        
        # العنوان الرئيسي
        title_frame = ctk.CTkFrame(
            main_scroll,
            height=80,
            corner_radius=10,
            fg_color=("#2b2b2b", "#1a1a1a"),
            border_width=2,
            border_color=self.cyber_colors['neon_green']
        )
        title_frame.pack(fill="x", pady=(0, 20))
        title_frame.pack_propagate(False)
        
        title_label = ctk.CTkLabel(
            title_frame,
            text="◢◤ CYBER SECURITY LEARNING CENTER ◥◣",
            font=ctk.CTkFont(size=26, weight="bold", family="Consolas"),
            text_color=self.cyber_colors['neon_green']
        )
        title_label.pack(pady=20)
        
        # قسم الدورات التدريبية
        courses_frame = ctk.CTkFrame(
            main_scroll,
            corner_radius=10,
            fg_color=("#2b2b2b", "#1a1a1a"),
            border_width=1,
            border_color=self.cyber_colors['neon_blue']
        )
        courses_frame.pack(fill="x", pady=(0, 20))
        
        courses_title = ctk.CTkLabel(
            courses_frame,
            text="┌─ CYBERSECURITY COURSES ─┐",
            font=ctk.CTkFont(size=16, weight="bold", family="Consolas"),
            text_color=self.cyber_colors['neon_blue']
        )
        courses_title.pack(pady=(15, 10))
        
        # شبكة الدورات
        courses_grid = ctk.CTkFrame(courses_frame, fg_color="transparent")
        courses_grid.pack(fill="x", padx=20, pady=(0, 20))
        
        # الصف الأول من الدورات
        row1_frame = ctk.CTkFrame(courses_grid, fg_color="transparent")
        row1_frame.pack(fill="x", pady=(0, 10))
        
        # دورة أساسيات الأمن السيبراني
        basics_btn = self.create_course_card(
            row1_frame,
            "🛡️ CYBERSECURITY BASICS",
            "Learn fundamental concepts of cybersecurity",
            "Beginner",
            self.cyber_colors['neon_green'],
            lambda: self.start_course("basics")
        )
        basics_btn.pack(side="left", fill="both", expand=True, padx=(0, 10))
        
        # دورة اختبار الاختراق
        pentest_btn = self.create_course_card(
            row1_frame,
            "🔍 PENETRATION TESTING",
            "Ethical hacking and vulnerability assessment",
            "Advanced",
            self.cyber_colors['neon_red'],
            lambda: self.start_course("pentest")
        )
        pentest_btn.pack(side="left", fill="both", expand=True, padx=5)
        
        # دورة أمن الشبكات
        network_btn = self.create_course_card(
            row1_frame,
            "📡 NETWORK SECURITY",
            "Secure network design and monitoring",
            "Intermediate",
            self.cyber_colors['neon_blue'],
            lambda: self.start_course("network")
        )
        network_btn.pack(side="left", fill="both", expand=True, padx=(10, 0))
        
        # الصف الثاني من الدورات
        row2_frame = ctk.CTkFrame(courses_grid, fg_color="transparent")
        row2_frame.pack(fill="x")
        
        # دورة التشفير
        crypto_btn = self.create_course_card(
            row2_frame,
            "🔐 CRYPTOGRAPHY",
            "Encryption algorithms and implementation",
            "Advanced",
            self.cyber_colors['neon_purple'],
            lambda: self.start_course("crypto")
        )
        crypto_btn.pack(side="left", fill="both", expand=True, padx=(0, 10))
        
        # دورة الاستجابة للحوادث
        incident_btn = self.create_course_card(
            row2_frame,
            "🚨 INCIDENT RESPONSE",
            "Handle and investigate security incidents",
            "Expert",
            self.cyber_colors['neon_orange'],
            lambda: self.start_course("incident")
        )
        incident_btn.pack(side="left", fill="both", expand=True, padx=5)
        
        # دورة الأمن السحابي
        cloud_btn = self.create_course_card(
            row2_frame,
            "☁️ CLOUD SECURITY",
            "Secure cloud infrastructure and services",
            "Intermediate",
            self.cyber_colors['electric_blue'],
            lambda: self.start_course("cloud")
        )
        cloud_btn.pack(side="left", fill="both", expand=True, padx=(10, 0))
        
        # قسم المحاكاة التفاعلية
        simulation_frame = ctk.CTkFrame(
            main_scroll,
            corner_radius=10,
            fg_color=("#2b2b2b", "#1a1a1a"),
            border_width=1,
            border_color=self.cyber_colors['neon_purple']
        )
        simulation_frame.pack(fill="x", pady=(0, 20))
        
        sim_title = ctk.CTkLabel(
            simulation_frame,
            text="┌─ INTERACTIVE SIMULATIONS ─┐",
            font=ctk.CTkFont(size=16, weight="bold", family="Consolas"),
            text_color=self.cyber_colors['neon_purple']
        )
        sim_title.pack(pady=(15, 10))
        
        # أزرار المحاكاة
        sim_buttons_frame = ctk.CTkFrame(simulation_frame, fg_color="transparent")
        sim_buttons_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        # محاكاة هجوم فيروسات
        virus_sim_btn = ctk.CTkButton(
            sim_buttons_frame,
            text="🦠 MALWARE SIMULATION",
            width=200,
            height=50,
            font=ctk.CTkFont(size=12, family="Consolas"),
            fg_color=self.cyber_colors['neon_red'],
            hover_color="#CC0000",
            text_color="white",
            command=lambda: self.start_simulation("malware")
        )
        virus_sim_btn.pack(side="left", padx=(0, 10))
        
        # محاكاة هجوم شبكة
        network_sim_btn = ctk.CTkButton(
            sim_buttons_frame,
            text="🌐 NETWORK ATTACK SIM",
            width=200,
            height=50,
            font=ctk.CTkFont(size=12, family="Consolas"),
            fg_color=self.cyber_colors['neon_blue'],
            hover_color=self.cyber_colors['electric_blue'],
            text_color="white",
            command=lambda: self.start_simulation("network_attack")
        )
        network_sim_btn.pack(side="left", padx=5)
        
        # محاكاة هندسة اجتماعية
        social_sim_btn = ctk.CTkButton(
            sim_buttons_frame,
            text="👥 SOCIAL ENGINEERING",
            width=200,
            height=50,
            font=ctk.CTkFont(size=12, family="Consolas"),
            fg_color=self.cyber_colors['neon_orange'],
            hover_color="#CC6600",
            text_color="white",
            command=lambda: self.start_simulation("social_eng")
        )
        social_sim_btn.pack(side="left", padx=5)
        
        # محاكاة اختبار اختراق
        pentest_sim_btn = ctk.CTkButton(
            sim_buttons_frame,
            text="🔓 PENETRATION TEST",
            width=200,
            height=50,
            font=ctk.CTkFont(size=12, family="Consolas"),
            fg_color=self.cyber_colors['neon_green'],
            hover_color="#00CC33",
            text_color="black",
            command=lambda: self.start_simulation("pentest")
        )
        pentest_sim_btn.pack(side="right")
        
        # قسم التقدم والإنجازات
        progress_frame = ctk.CTkFrame(
            main_scroll,
            corner_radius=10,
            fg_color=("#2b2b2b", "#1a1a1a"),
            border_width=1,
            border_color=self.cyber_colors['neon_orange']
        )
        progress_frame.pack(fill="x", pady=(0, 20))
        
        progress_title = ctk.CTkLabel(
            progress_frame,
            text="┌─ YOUR PROGRESS ─┐",
            font=ctk.CTkFont(size=16, weight="bold", family="Consolas"),
            text_color=self.cyber_colors['neon_orange']
        )
        progress_title.pack(pady=(15, 10))
        
        # إحصائيات التقدم
        stats_frame = ctk.CTkFrame(progress_frame, fg_color="transparent")
        stats_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        # الدورات المكتملة
        completed_card = self.create_progress_card(
            stats_frame, "COURSES COMPLETED", "3/6", self.cyber_colors['neon_green']
        )
        completed_card.pack(side="left", fill="both", expand=True, padx=(0, 10))
        
        # المحاكاة المكتملة
        simulations_card = self.create_progress_card(
            stats_frame, "SIMULATIONS DONE", "8/12", self.cyber_colors['neon_blue']
        )
        simulations_card.pack(side="left", fill="both", expand=True, padx=5)
        
        # النقاط المكتسبة
        points_card = self.create_progress_card(
            stats_frame, "POINTS EARNED", "2,450", self.cyber_colors['neon_purple']
        )
        points_card.pack(side="left", fill="both", expand=True, padx=5)
        
        # المستوى الحالي
        level_card = self.create_progress_card(
            stats_frame, "CURRENT LEVEL", "Expert", self.cyber_colors['neon_orange']
        )
        level_card.pack(side="left", fill="both", expand=True, padx=(10, 0))
        
        # قسم الموارد والأدوات
        resources_frame = ctk.CTkFrame(
            main_scroll,
            corner_radius=10,
            fg_color=("#2b2b2b", "#1a1a1a"),
            border_width=1,
            border_color=self.cyber_colors['matrix_green']
        )
        resources_frame.pack(fill="both", expand=True)
        
        resources_title = ctk.CTkLabel(
            resources_frame,
            text="┌─ RESOURCES & TOOLS ─┐",
            font=ctk.CTkFont(size=16, weight="bold", family="Consolas"),
            text_color=self.cyber_colors['matrix_green']
        )
        resources_title.pack(pady=(15, 10))
        
        # قائمة الموارد
        resources_list = ctk.CTkScrollableFrame(
            resources_frame,
            height=200,
            fg_color=("#3b3b3b", "#2a2a2a")
        )
        resources_list.pack(fill="both", expand=True, padx=15, pady=(0, 15))
        
        # إضافة الموارد
        self.add_resources(resources_list)
    
    def create_course_card(self, parent, title, description, level, color, command):
        """إنشاء بطاقة دورة تدريبية"""
        card = ctk.CTkFrame(
            parent,
            height=120,
            corner_radius=10,
            fg_color=("#2b2b2b", "#1a1a1a"),
            border_width=2,
            border_color=color
        )
        card.pack_propagate(False)
        
        # العنوان
        title_label = ctk.CTkLabel(
            card,
            text=title,
            font=ctk.CTkFont(size=12, weight="bold", family="Consolas"),
            text_color=color
        )
        title_label.pack(pady=(10, 5))
        
        # الوصف
        desc_label = ctk.CTkLabel(
            card,
            text=description,
            font=ctk.CTkFont(size=9, family="Consolas"),
            text_color=self.cyber_colors['neon_green'],
            wraplength=150
        )
        desc_label.pack(pady=(0, 5))
        
        # المستوى
        level_label = ctk.CTkLabel(
            card,
            text=f"Level: {level}",
            font=ctk.CTkFont(size=8, family="Consolas"),
            text_color=self.cyber_colors['neon_blue']
        )
        level_label.pack(pady=(0, 5))
        
        # زر البدء
        start_btn = ctk.CTkButton(
            card,
            text="START",
            width=80,
            height=25,
            font=ctk.CTkFont(size=10, family="Consolas"),
            fg_color=color,
            hover_color=color,
            text_color="black" if color == self.cyber_colors['neon_green'] else "white",
            command=command
        )
        start_btn.pack(pady=(0, 10))
        
        return card
    
    def create_progress_card(self, parent, title, value, color):
        """إنشاء بطاقة تقدم"""
        card = ctk.CTkFrame(
            parent,
            height=80,
            corner_radius=10,
            fg_color=("#2b2b2b", "#1a1a1a"),
            border_width=1,
            border_color=color
        )
        card.pack_propagate(False)
        
        # العنوان
        title_label = ctk.CTkLabel(
            card,
            text=title,
            font=ctk.CTkFont(size=9, family="Consolas"),
            text_color=color
        )
        title_label.pack(pady=(10, 2))
        
        # القيمة
        value_label = ctk.CTkLabel(
            card,
            text=value,
            font=ctk.CTkFont(size=16, weight="bold", family="Consolas"),
            text_color=color
        )
        value_label.pack(pady=(0, 10))
        
        return card

    def add_resources(self, parent):
        """إضافة الموارد والأدوات"""
        resources = [
            ("📚 NIST Cybersecurity Framework", "Comprehensive cybersecurity guidelines", "https://nist.gov/cybersecurity"),
            ("🔧 Kali Linux Tools", "Penetration testing toolkit", "https://kali.org/tools"),
            ("📖 OWASP Top 10", "Web application security risks", "https://owasp.org/top10"),
            ("🛡️ CIS Controls", "Critical security controls", "https://cisecurity.org/controls"),
            ("🔍 CVE Database", "Common vulnerabilities database", "https://cve.mitre.org"),
            ("📋 Security Checklists", "System hardening guides", "Internal Resource"),
            ("🎯 Practice Labs", "Hands-on security exercises", "Internal Labs"),
            ("📺 Video Tutorials", "Step-by-step demonstrations", "Internal Videos")
        ]

        for i, (title, description, link) in enumerate(resources):
            resource_frame = ctk.CTkFrame(
                parent,
                fg_color="transparent" if i % 2 == 0 else ("#4b4b4b", "#3a3a3a")
            )
            resource_frame.pack(fill="x", pady=2)

            # أيقونة ونص
            content_frame = ctk.CTkFrame(resource_frame, fg_color="transparent")
            content_frame.pack(fill="x", padx=10, pady=5)

            # العنوان
            title_label = ctk.CTkLabel(
                content_frame,
                text=title,
                font=ctk.CTkFont(size=11, weight="bold", family="Consolas"),
                text_color=self.cyber_colors['neon_green']
            )
            title_label.pack(anchor="w")

            # الوصف
            desc_label = ctk.CTkLabel(
                content_frame,
                text=description,
                font=ctk.CTkFont(size=9, family="Consolas"),
                text_color=self.cyber_colors['neon_blue']
            )
            desc_label.pack(anchor="w")

            # الرابط
            link_label = ctk.CTkLabel(
                content_frame,
                text=f"🔗 {link}",
                font=ctk.CTkFont(size=8, family="Consolas"),
                text_color=self.cyber_colors['neon_purple']
            )
            link_label.pack(anchor="w")

    def start_course(self, course_type):
        """بدء دورة تدريبية"""
        self.current_lesson = course_type

        # تسجيل العملية
        self.log_action("course_start", f"Started {course_type} course")

        # عرض رسالة بدء الدورة
        from tkinter import messagebox
        messagebox.showinfo(
            "🎓 COURSE STARTED",
            f"Starting {course_type.upper()} course!\n\nThis is a simulation of the course content.\nIn a real implementation, this would open detailed lessons and interactive content."
        )

    def start_simulation(self, sim_type):
        """بدء محاكاة تفاعلية"""
        # تسجيل العملية
        self.log_action("simulation_start", f"Started {sim_type} simulation")

        # عرض رسالة بدء المحاكاة
        from tkinter import messagebox
        messagebox.showinfo(
            "🎯 SIMULATION STARTED",
            f"Starting {sim_type.upper()} simulation!\n\nThis is a simulation preview.\nIn a real implementation, this would launch an interactive cybersecurity exercise."
        )
